// @ts-nocheck - Temporary fix for multi-agent type integration
import React, { useState } from 'react'
import { 
  BarChart3,
  FileText,
  Brain,
  Loader2,
  CheckCircle,
  BookOpen,
  GraduationCap,
  Cpu,
  Zap
} from 'lucide-react'
import { Card, CardContent } from './ui/card'
import { Button } from './ui/button'
import { Progress } from './ui/progress'
import { Badge } from './ui/badge'
import { StructuredReviewViewer } from './StructuredReviewViewer'
import { EnhancedInlineDiffViewer } from './EnhancedInlineDiffViewer'
import { EnhancedReviewReport } from './EnhancedReviewReport'
import { TutorialSummaryViewer } from './TutorialSummaryViewer'
import { FallbackMonitoringAlert } from './FallbackMonitoringAlert'
import { DetailedFallbackReport } from './DetailedFallbackReport'
import { DataFlowDebugger } from './DataFlowDebugger'
import { DataValidator } from '../utils/dataValidation'
import type { ReviewSession, EnhancedReviewResults } from '../types/enhanced-review'
import type { BitbucketPullRequest, BitbucketRepository } from '../types/bitbucket.types'
import type { StructuredReviewResult } from '../services/codeReviewer/CodeReviewerService'
import type { 
  EnhancedReviewResults as MultiAgentEnhancedResults
} from '../types/multi-agent'
import { convertReviewResultsToAIFormat } from '../utils/reviewDataConverter'
import { MultiAgentResultsAggregator } from './MultiAgentResultsAggregator'

/**
 * Simple adapter for EnhancedReviewReport component
 * Since the backend doesn't provide enhanced_report field,
 * we create a minimal fallback when needed
 */
const createFallbackEnhancedReport = (results: StructuredReviewResult | EnhancedReviewResults): StructuredReviewResult | EnhancedReviewResults => {
  // Handle StructuredReviewResult format
  if ('structured_data' in results && results.structured_data) {
    return results // EnhancedReviewReport already handles this format
  }
  
  // Handle legacy EnhancedReviewResults format - don't modify if it already has what we need
  return results
}

type ResultsView = 'overview' | 'inline-diff' | 'enhanced-report' | 'tutorial-summary' | 'multi-agent-overview'

interface AssignedPR {
  id: number
  title: string
  branch: string
  repository: string
  workspace: string
}

// Helper function to detect Multi-Agent results
const isMultiAgentResults = (results: any): results is MultiAgentEnhancedResults => {
  return results && 
         typeof results === 'object' &&
         'agent_results' in results &&
         'overall_results' in results &&
         results.overall_results &&
         'execution_metrics' in results.overall_results
}


interface ReviewResultsStepProps {
  activeSession: ReviewSession | null
  selectedPR: AssignedPR | null
  onSessionUpdate: (sessionId: string, updates: Partial<ReviewSession>) => void
}

export const ReviewResultsStep: React.FC<ReviewResultsStepProps> = ({
  activeSession,
  selectedPR,
  onSessionUpdate
}) => {
  // Detect Multi-Agent results and set initial view accordingly
  const isMultiAgent = activeSession?.results ? isMultiAgentResults(activeSession.results) : false
  const [resultsView, setResultsView] = useState<ResultsView>(
    isMultiAgent ? 'multi-agent-overview' : 'enhanced-report'
  )

  console.log('🔍 ReviewResultsStep rendering')
  console.log('🔍 activeSession:', activeSession)
  console.log('🔍 isMultiAgent:', isMultiAgent)
  console.log('🔍 activeSession?.results:', activeSession?.results)
  console.log('🔍 activeSession?.status:', activeSession?.status)

  // Data validation and debugging
  if (activeSession?.results) {
    console.log('📊 DATA DEBUGGING - Full Results Object:', JSON.stringify(activeSession.results, null, 2))
    
    // Validate the complete results object
    const validationResult = DataValidator.validateReviewResults(activeSession.results)
    
    if (!validationResult.isValid) {
      console.error('🚨 DATA VALIDATION FAILED:', validationResult.errors)
    }
    
    if (validationResult.warnings.length > 0) {
      console.warn('⚠️ DATA VALIDATION WARNINGS:', validationResult.warnings)
    }
    
    // Update the session with validated data
    if (validationResult.data && validationResult.data !== activeSession.results) {
      console.log('🔧 DATA VALIDATION - Applying data corrections')
      onSessionUpdate(activeSession.session_id, {
        results: validationResult.data
      })
    }
  }

  if (!activeSession?.results) {
    console.log('❌ No results available - showing loading or error state')
    
    if (activeSession?.status === 'completed') {
      // Review is completed but no results yet - show enhanced loading state
      const currentProgress = activeSession.progress || 90;
      const progressMessage = activeSession.progress_message || 'Finalizing enhanced report...';
      
      return (
        <Card className="border-blue-500/50 bg-blue-500/5">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="relative mb-6">
                <Loader2 className="h-12 w-12 text-blue-500 mx-auto animate-spin" />
                <CheckCircle className="h-6 w-6 text-green-500 absolute -bottom-1 -right-1 bg-white rounded-full" />
              </div>
              <h3 className="text-lg font-semibold text-blue-600 mb-2">
                Generiere umfassendes Review-Protokoll...
              </h3>
              <p className="text-blue-500/80 text-sm mb-4">
                AI-Analyse abgeschlossen. Das detaillierte Review-Protokoll wird erstellt.
              </p>
              
              {/* Progress details */}
              <div className="max-w-md mx-auto mb-4">
                <div className="flex justify-between text-sm text-blue-600 mb-2">
                  <span>Fortschritt</span>
                  <span>{Math.round(currentProgress)}%</span>
                </div>
                <Progress value={currentProgress} className="h-2 mb-2" />
                <p className="text-xs text-blue-500/70">
                  {progressMessage}
                </p>
              </div>

              {/* What's happening */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 text-left">
                <h4 className="font-medium text-blue-800 mb-2 text-sm">Was passiert gerade:</h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>✅ Code-Analyse mit Claude AI abgeschlossen</li>
                  <li>✅ Acceptance Criteria Compliance geprüft</li>
                  <li>✅ Quality Score und Metrics berechnet</li>
                  <li>🔄 Enhanced Report wird zusammengestellt...</li>
                  <li>⏳ Strukturierte Findings werden finalisiert...</li>
                </ul>
              </div>
              
              <Button 
                variant="outline" 
                size="sm" 
                className="border-blue-500 text-blue-500 hover:bg-blue-500/10"
                onClick={() => window.location.reload()}
              >
                Seite aktualisieren
              </Button>
            </div>
          </CardContent>
        </Card>
      )
    }
    
    return null
  }

  const handleFileClick = (filename: string, line?: number) => {
    // Switch to inline diff view and navigate to the file
    setResultsView('inline-diff')
    setTimeout(() => {
      const elementId = line ? `file-${filename.replace(/[^a-zA-Z0-9]/g, '-')}-line-${line}` : `file-${filename.replace(/[^a-zA-Z0-9]/g, '-')}`
      const element = document.getElementById(elementId)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }, 100)
  }

  const handleExportResults = (results: StructuredReviewResult | EnhancedReviewResults) => {
    const dataStr = JSON.stringify(results, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    
    // Handle different result formats for filename
    const sessionId = results.session_id
    const branchName = 'structured_data' in results ?
      results.metadata?.branch_name || 'unknown' :
      results.branch_name || 'unknown'

    // Safely handle branch name replacement
    const safeBranchName = (branchName || 'unknown').replace(/[\/\\:*?"<>|]/g, '-')
    link.download = `review-${sessionId}-${safeBranchName}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const handleAIFindingNavigation = (finding: { file?: string; line?: number }) => {
    if (finding.file) {
      handleFileClick(finding.file, finding.line)
    }
  }

  // Convert PR info for EnhancedInlineDiffViewer
  const pullRequestInfo: BitbucketPullRequest | null = selectedPR ? {
    id: selectedPR.id,
    title: selectedPR.title,
    source: {
      branch: {
        name: selectedPR.branch
      }
    }
  } as BitbucketPullRequest : null

  const repositoryInfo: BitbucketRepository | null = selectedPR ? {
    name: selectedPR.repository.split('/').pop() || selectedPR.repository,
    workspace: {
      slug: selectedPR.workspace
    }
  } as BitbucketRepository : null

  // Convert results to AI format
  const convertedResults = activeSession.results ? 
    convertReviewResultsToAIFormat(activeSession.results) : null

  return (
    <div className="space-y-6">
      {/* Results View Tabs */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <h3 className="text-lg font-semibold">Review Results</h3>
            <div className="flex items-center gap-1 bg-muted rounded-lg p-1">
              {/* Multi-Agent Overview Tab - only show for Multi-Agent results */}
              {isMultiAgent && (
                <Button
                  variant={resultsView === 'multi-agent-overview' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setResultsView('multi-agent-overview')}
                  className="flex items-center gap-2 bg-gradient-to-r from-violet-500/10 to-purple-500/10 border-violet-500/30 hover:bg-violet-500/20"
                >
                  <Cpu className="h-4 w-4" />
                  Multi-Agent
                  <Badge variant="secondary" className="text-xs ml-1">7 Agents</Badge>
                  <Zap className="h-3 w-3 text-violet-600 animate-pulse" />
                </Button>
              )}
              
              <Button
                variant={resultsView === 'overview' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setResultsView('overview')}
                className="flex items-center gap-2"
              >
                <BarChart3 className="h-4 w-4" />
                Overview
              </Button>
              <Button
                variant={resultsView === 'enhanced-report' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setResultsView('enhanced-report')}
                className="flex items-center gap-2 bg-gradient-to-r from-primary/10 to-blue-500/10 border-primary/30 hover:bg-primary/20"
              >
                <FileText className="h-4 w-4" />
                Enhanced Report
                <Brain className="h-3 w-3 text-primary animate-pulse" />
              </Button>
              <Button
                variant={resultsView === 'inline-diff' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setResultsView('inline-diff')}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                Inline Diff
                <Brain className="h-3 w-3 text-primary" />
              </Button>
              <Button
                variant={resultsView === 'tutorial-summary' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setResultsView('tutorial-summary')}
                className="flex items-center gap-2 bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border-emerald-500/30 hover:bg-emerald-500/20"
              >
                <BookOpen className="h-4 w-4" />
                Tutorial
                <GraduationCap className="h-3 w-3 text-emerald-600 animate-pulse" />
              </Button>
            </div>
          </div>
          
          <p className="text-sm text-muted-foreground mt-2">
            {resultsView === 'multi-agent-overview'
              ? '🤖 Parallel analysis by 7 specialized AI agents with performance metrics, agent-specific findings, and execution timeline'
              : resultsView === 'overview' 
                ? 'View categorized AI findings and review summary'
                : resultsView === 'enhanced-report'
                  ? '🚀 CLI-quality comprehensive report with detailed AC compliance analysis, code quality metrics, bug detection, and actionable recommendations'
                  : resultsView === 'inline-diff'
                    ? 'See AI findings directly in the code diff alongside manual comments'
                    : '📚 Tutorial-style implementation guide with architecture overview, business context, and step-by-step documentation'
            }
          </p>
        </CardContent>
      </Card>

      {/* Fallback Monitoring Alert */}
      <FallbackMonitoringAlert 
        aiMetadata={'enhanced_report' in (activeSession.results || {}) ? 
          (activeSession.results as EnhancedReviewResults).enhanced_report?.ai_analysis_metadata : undefined}
        effortAnalysis={'enhanced_report' in (activeSession.results || {}) ? 
          (activeSession.results as EnhancedReviewResults).enhanced_report?.effort_estimation : undefined}
      />

      {/* Detailed Fallback Report (Developer Tools) */}
      <DetailedFallbackReport
        aiMetadata={'enhanced_report' in (activeSession.results || {}) ? 
          (activeSession.results as EnhancedReviewResults).enhanced_report?.ai_analysis_metadata : undefined}
        effortAnalysis={'enhanced_report' in (activeSession.results || {}) ? 
          (activeSession.results as EnhancedReviewResults).enhanced_report?.effort_estimation : undefined}
        className="mb-6"
      />

      {/* Data Flow Debugger (Developer Tools) */}
      <DataFlowDebugger
        backendData={activeSession.results}
        frontendData={convertedResults}
        className="mb-6"
      />

      {/* Results Content */}
      {resultsView === 'multi-agent-overview' ? (
        isMultiAgent ? (
          <MultiAgentResultsAggregator
            results={activeSession.results as unknown as MultiAgentEnhancedResults}
            onFileClick={handleFileClick}
            onExportResults={(results) => {
              handleExportResults(results)
            }}
            className="max-w-none"
          />
        ) : (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <Cpu className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-yellow-800 mb-2">
                  Multi-Agent Results Not Available
                </h3>
                <p className="text-yellow-700 text-sm">
                  This review was not conducted using the Multi-Agent service. 
                  Switch to another view to see the available results.
                </p>
              </div>
            </CardContent>
          </Card>
        )
      ) : resultsView === 'overview' ? (
        <StructuredReviewViewer
          results={activeSession.results}
          onFileClick={handleFileClick}
          onExportResults={handleExportResults}
          className="max-w-none"
        />
      ) : resultsView === 'enhanced-report' ? (
        <EnhancedReviewReport
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          results={createFallbackEnhancedReport(activeSession.results) as any}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onExportReport={(results: any) => handleExportResults(results)}
          className="max-w-none"
        />
      ) : resultsView === 'tutorial-summary' ? (
        <TutorialSummaryViewer
          results={activeSession.results}
          onExportTutorial={(tutorial) => {
            const dataStr = JSON.stringify(tutorial, null, 2)
            const dataBlob = new Blob([dataStr], { type: 'application/json' })
            const url = URL.createObjectURL(dataBlob)
            const link = document.createElement('a')
            link.href = url
            link.download = `tutorial-${tutorial.tutorial_id || 'summary'}.json`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            URL.revokeObjectURL(url)
          }}
          className="max-w-none"
        />
      ) : (
        <EnhancedInlineDiffViewer
          pullRequest={pullRequestInfo}
          repository={repositoryInfo}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          aiReviewResults={convertedResults as any}
          showAIFindings={true}
          onNavigateToFinding={handleAIFindingNavigation}
          className="max-w-none"
        />
      )}
    </div>
  )
}