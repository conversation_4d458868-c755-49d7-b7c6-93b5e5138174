/**
 * Enhanced Review Interface - ALIGNED WITH ACTUAL BACKEND RESPONSE
 * Based on backend/core/services/review_service.py:319-397
 */

import type { 
  AIAnalysisMetadata, 
  EffortEstimationAnalysis 
} from './fallback-monitoring';

import type { EnhancedReviewResults as MultiAgentResults } from './multi-agent';

export interface ReviewFinding {
  text: string;
  severity: 'high' | 'medium' | 'low';
  file?: string;
  line?: number;
  suggestion?: string;
}

/**
 * ACTUAL Backend Response Structure from review_service.py
 * This matches what the backend actually returns
 */
export interface EnhancedReviewResults {
  session_id: string;
  review_mode: string;
  branch_name: string;
  pr_url?: string;
  worktree_path?: string;
  timestamp: string;
  raw_review: string;
  metadata: {
    changed_files: string[];
    diff_summary?: string;
    file_count: number;
  };
  jira_ticket?: {
    ticket_id: string;
    summary: string;
    status: string;
    acceptance_criteria_count: number;
    acceptance_criteria: string[];
  };
  structured_findings: {
    acceptance_criteria: ReviewFinding[];
    code_quality: ReviewFinding[];
    security_issues: ReviewFinding[];
    performance_issues: ReviewFinding[];
    bugs: ReviewFinding[];
    suggestions: ReviewFinding[];
  };
  summary: {
    total_findings: number;
    high_severity_count: number;
    files_changed: number;
    review_length: number;
    categories: Record<string, number>;
    completion_status: string;
  };
  
  // NOTE: enhanced_report is NOT returned by current backend
  // It exists in old API files but not in current review_service.py
  // This field is optional for backwards compatibility
  enhanced_report?: {
    ai_analysis_metadata?: AIAnalysisMetadata;
    effort_estimation?: EffortEstimationAnalysis;
    code_quality_analysis?: {
      executive_summary?: {
        overall_score: number;
        critical_issues: number;
        code_smells: number;
        duplication_level: number;
      };
    };
  };
}

import type { StructuredReviewResult } from '../services/codeReviewer/CodeReviewerService'

export interface ReviewSession {
  session_id: string;
  branch_name: string;
  pr_url?: string;
  status: 'initializing' | 'running' | 'completed' | 'error';
  progress: number;
  progress_message?: string;
  created_at: string;
  completed_at?: string;
  results?: StructuredReviewResult | EnhancedReviewResults | MultiAgentResults;
  error?: string;
  worktree_path?: string;
  structured_summary?: any; // For SDK-based results
  tutorial?: any; // For Phase 3 results
  // Multi-agent specific fields
  review_mode?: string;
  is_multi_agent?: boolean;
}

// Multi-Agent specific types
export interface AgentStatus {
  agent_type: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  progress: number;
  started_at?: string;
  completed_at?: string;
  estimated_remaining_time?: number;
  error_message?: string;
}

export interface MultiAgentReviewSession extends ReviewSession {
  review_id: string; // UUID for multi-agent reviews
  agent_statuses: Record<string, AgentStatus>;
  active_agents: string[];
  completed_agents: string[];
  failed_agents: string[];
  context_status?: Record<string, string>;
  estimated_completion_time?: number;
}

export interface AgentResult {
  agent_type: string;
  status: 'completed' | 'failed';
  execution_time: number;
  result_data: Record<string, any>;
  findings: Array<{
    type: string;
    severity?: 'high' | 'medium' | 'low';
    description: string;
    file?: string;
    line?: number;
    suggestion?: string;
  }>;
  recommendations: string[];
  confidence_score?: number;
  metadata: Record<string, any>;
}

export interface MultiAgentReviewResult {
  review_id: string;
  status: 'completed' | 'failed' | 'cancelled';
  overall_results: Record<string, any>;
  reports: Record<string, string>; // markdown, html, etc.
  execution_time: number;
  agent_results: Record<string, AgentResult>;
  context_metadata?: Record<string, any>;
  performance_metrics?: Record<string, any>;
  created_at: string;
  summary?: string;
  priority_findings: Array<{
    priority: 'high' | 'medium' | 'low';
    type: string;
    description: string;
    agent_type: string;
  }>;
}