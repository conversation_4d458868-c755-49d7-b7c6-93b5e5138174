# code-reviewer-service/src/models/context/web_search_context.py
"""
WebSearchContext - Specialized Context für External Knowledge Integration
Erweitert BaseContext für Web Search und Knowledge Integration
"""

import json
import asyncio
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from urllib.parse import urlparse, quote_plus
import logging

from .base_context import BaseContext, ContextType, ContextFactory


class WebSearchContext(BaseContext):
    """
    WebSearchContext für External Knowledge Integration und Web Search
    
    Verantwortlichkeiten:
    - Web Search API Integration
    - Knowledge Base Queries
    - Result Caching and Processing
    - Content Extraction
    - Relevance Scoring
    """
    
    def __init__(
        self,
        context_id: str,
        search_queries: List[str],
        search_config: Optional[Dict[str, Any]] = None,
        validation_strategy=None,
        extraction_strategy=None
    ):
        super().__init__(
            context_id=context_id,
            context_type=ContextType.WEB_SEARCH,
            validation_strategy=validation_strategy,
            extraction_strategy=extraction_strategy
        )
        
        self.search_queries = search_queries if isinstance(search_queries, list) else [search_queries]
        self.search_config = search_config or {}
        
        # Search configuration
        self.max_results_per_query = self.search_config.get('max_results', 10)
        self.search_timeout = self.search_config.get('timeout', 30)
        self.cache_duration = timedelta(hours=self.search_config.get('cache_hours', 24))
        
        # Search providers configuration
        self.search_providers = self.search_config.get('providers', ['web', 'documentation'])
        self.api_keys = self.search_config.get('api_keys', {})
        
        # Content filtering
        self.allowed_domains = self.search_config.get('allowed_domains', [])
        self.blocked_domains = self.search_config.get('blocked_domains', [])
        self.content_types = self.search_config.get('content_types', ['documentation', 'tutorial', 'reference'])
        
        # Search results storage
        self.search_results = {}
        self.processed_results = {}
        self.knowledge_base = {}
        
        # Performance tracking
        self.search_stats = {
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'cache_hits': 0,
            'total_results': 0,
            'processing_time': 0
        }
        
        self._logger.info(f"Initialized WebSearchContext with {len(self.search_queries)} queries")

    @classmethod
    def create(
        cls,
        context_id: str,
        source_data: Any,
        config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> 'WebSearchContext':
        """Factory method für WebSearchContext creation"""
        
        # source_data kann search queries (str/list) oder Dict mit Details sein
        if isinstance(source_data, str):
            search_queries = [source_data]
        elif isinstance(source_data, list):
            search_queries = source_data
        elif isinstance(source_data, dict):
            search_queries = source_data.get('queries', [])
            if not search_queries:
                search_queries = [source_data.get('query', '')]
        else:
            raise ValueError(f"Invalid source_data type for WebSearchContext: {type(source_data)}")
        
        # Filter empty queries
        search_queries = [q.strip() for q in search_queries if q and q.strip()]
        
        if not search_queries:
            raise ValueError("At least one search query is required for WebSearchContext")
        
        # Extract search config from config
        search_config = None
        if config:
            search_config = config.get('web_search', config)
        
        return cls(
            context_id=context_id,
            search_queries=search_queries,
            search_config=search_config,
            **kwargs
        )

    async def _validate_specific(self) -> bool:
        """Spezifische Validation für WebSearchContext"""
        try:
            # Validate search queries
            if not self.search_queries:
                self._logger.error("No search queries provided")
                return False
            
            # Validate query format
            for query in self.search_queries:
                if not query or len(query.strip()) < 3:
                    self._logger.error(f"Invalid query format: {query}")
                    return False
            
            # Validate search providers
            valid_providers = ['web', 'documentation', 'github', 'stackoverflow', 'custom']
            for provider in self.search_providers:
                if provider not in valid_providers:
                    self._logger.warning(f"Unknown search provider: {provider}")
            
            # Test connectivity (optional)
            if self.search_config.get('test_connectivity', False):
                await self._test_search_connectivity()
            
            return True
            
        except Exception as e:
            self._logger.error(f"WebSearchContext validation error: {str(e)}")
            return False

    async def _build_specific_agent_context(self) -> Dict[str, Any]:
        """Build spezifischen Agent Context für Web Search"""
        
        # Ensure we have search results
        if not self.search_results:
            await self._perform_searches()
        
        # Process results if not already done
        if not self.processed_results:
            await self._process_search_results()
        
        return {
            "search_config": {
                "queries": self.search_queries,
                "providers": self.search_providers,
                "max_results": self.max_results_per_query,
                "timeout": self.search_timeout
            },
            "search_results": self.processed_results,
            "knowledge_base": self.knowledge_base,
            "search_statistics": self.search_stats,
            "relevant_sources": self._get_top_sources(),
            "extracted_knowledge": self._extract_key_knowledge(),
            "search_metadata": {
                "last_search": self.last_updated.isoformat(),
                "cache_valid_until": (self.last_updated + self.cache_duration).isoformat(),
                "total_sources": sum(len(results) for results in self.search_results.values()),
                "unique_domains": len(self._get_unique_domains())
            }
        }

    async def refresh(self) -> bool:
        """Refresh search results"""
        try:
            self._logger.info(f"Refreshing web search context: {self.context_id}")
            
            # Clear previous data
            self._raw_data.clear()
            self._processed_data.clear()
            self.search_results.clear()
            self.processed_results.clear()
            self.knowledge_base.clear()
            
            # Reset stats
            self.search_stats = {
                'total_queries': 0,
                'successful_queries': 0,
                'failed_queries': 0,
                'cache_hits': 0,
                'total_results': 0,
                'processing_time': 0
            }
            
            # Perform new searches
            await self._perform_searches()
            
            # Process results
            await self._process_search_results()
            
            # Update timestamp
            self.last_updated = datetime.now()
            
            return True
            
        except Exception as e:
            self._logger.error(f"Failed to refresh web search context: {str(e)}")
            return False

    # ==================== SEARCH EXECUTION ====================

    async def _perform_searches(self) -> None:
        """Perform searches for all queries"""
        self._logger.debug(f"Performing searches for {len(self.search_queries)} queries")
        
        start_time = datetime.now()
        
        # Execute searches in parallel
        search_tasks = []
        for query in self.search_queries:
            task = self._search_single_query(query)
            search_tasks.append(task)
        
        # Wait for all searches to complete
        results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        # Process results
        for i, result in enumerate(results):
            query = self.search_queries[i]
            if isinstance(result, Exception):
                self._logger.error(f"Search failed for query '{query}': {str(result)}")
                self.search_stats['failed_queries'] += 1
                self.search_results[query] = []
            else:
                self.search_results[query] = result
                self.search_stats['successful_queries'] += 1
                # Safe len() call with explicit type checking
                try:
                    if isinstance(result, (list, tuple, dict, set, str)) and not isinstance(result, Exception):
                        self.search_stats['total_results'] += len(result)
                    else:
                        self.search_stats['total_results'] += 1
                except (TypeError, AttributeError):
                    self.search_stats['total_results'] += 1
        
        self.search_stats['total_queries'] = len(self.search_queries)
        self.search_stats['processing_time'] = int((datetime.now() - start_time).total_seconds())
        
        self._logger.info(f"Search completed: {self.search_stats['successful_queries']}/{self.search_stats['total_queries']} successful")

    async def _search_single_query(self, query: str) -> List[Dict[str, Any]]:
        """Perform search for a single query"""
        results = []
        
        # Check cache first
        cached_results = await self._get_cached_results(query)
        if cached_results:
            self.search_stats['cache_hits'] += 1
            return cached_results
        
        # Execute search using configured providers
        for provider in self.search_providers:
            try:
                provider_results = await self._search_with_provider(query, provider)
                results.extend(provider_results)
            except Exception as e:
                self._logger.warning(f"Search provider '{provider}' failed for query '{query}': {str(e)}")
        
        # Deduplicate and limit results
        results = self._deduplicate_results(results)
        results = results[:self.max_results_per_query]
        
        # Cache results
        await self._cache_results(query, results)
        
        return results

    async def _search_with_provider(self, query: str, provider: str) -> List[Dict[str, Any]]:
        """Search using specific provider"""
        if provider == 'web':
            return await self._search_web(query)
        elif provider == 'documentation':
            return await self._search_documentation(query)
        elif provider == 'github':
            return await self._search_github(query)
        elif provider == 'stackoverflow':
            return await self._search_stackoverflow(query)
        elif provider == 'custom':
            return await self._search_custom(query)
        else:
            self._logger.warning(f"Unknown search provider: {provider}")
            return []

    async def _search_web(self, query: str) -> List[Dict[str, Any]]:
        """Perform web search (simulated implementation)"""
        # This would normally use a real search API like Google, Bing, or DuckDuckGo
        # For now, we'll return mock results
        
        self._logger.debug(f"Performing web search for: {query}")
        
        # Simulate API delay
        await asyncio.sleep(0.1)
        
        # Mock results based on query content
        mock_results = []
        
        if any(term in query.lower() for term in ['python', 'programming', 'code']):
            mock_results = [
                {
                    'title': f'Python Documentation - {query}',
                    'url': f'https://docs.python.org/3/search.html?q={quote_plus(query)}',
                    'snippet': f'Official Python documentation for {query}...',
                    'domain': 'docs.python.org',
                    'content_type': 'documentation',
                    'relevance_score': 0.9,
                    'last_updated': datetime.now().isoformat()
                },
                {
                    'title': f'{query} - Stack Overflow',
                    'url': f'https://stackoverflow.com/search?q={quote_plus(query)}',
                    'snippet': f'Community discussions about {query}...',
                    'domain': 'stackoverflow.com',
                    'content_type': 'community',
                    'relevance_score': 0.8,
                    'last_updated': datetime.now().isoformat()
                }
            ]
        
        return mock_results

    async def _search_documentation(self, query: str) -> List[Dict[str, Any]]:
        """Search documentation sources"""
        self._logger.debug(f"Searching documentation for: {query}")
        
        # Mock documentation search
        await asyncio.sleep(0.1)
        
        return [
            {
                'title': f'Documentation: {query}',
                'url': f'https://docs.example.com/search/{quote_plus(query)}',
                'snippet': f'Technical documentation covering {query}...',
                'domain': 'docs.example.com',
                'content_type': 'documentation',
                'relevance_score': 0.85,
                'last_updated': datetime.now().isoformat()
            }
        ]

    async def _search_github(self, query: str) -> List[Dict[str, Any]]:
        """Search GitHub repositories and code"""
        self._logger.debug(f"Searching GitHub for: {query}")
        
        # Mock GitHub search
        await asyncio.sleep(0.1)
        
        return [
            {
                'title': f'GitHub Repository: {query}',
                'url': f'https://github.com/search?q={quote_plus(query)}',
                'snippet': f'Open source code and repositories related to {query}...',
                'domain': 'github.com',
                'content_type': 'code',
                'relevance_score': 0.75,
                'last_updated': datetime.now().isoformat()
            }
        ]

    async def _search_stackoverflow(self, query: str) -> List[Dict[str, Any]]:
        """Search Stack Overflow"""
        self._logger.debug(f"Searching Stack Overflow for: {query}")
        
        # Mock Stack Overflow search
        await asyncio.sleep(0.1)
        
        return [
            {
                'title': f'Stack Overflow: {query}',
                'url': f'https://stackoverflow.com/search?q={quote_plus(query)}',
                'snippet': f'Developer community discussions about {query}...',
                'domain': 'stackoverflow.com',
                'content_type': 'community',
                'relevance_score': 0.8,
                'last_updated': datetime.now().isoformat()
            }
        ]

    async def _search_custom(self, query: str) -> List[Dict[str, Any]]:
        """Search custom knowledge sources"""
        self._logger.debug(f"Searching custom sources for: {query}")
        
        # Custom search implementation would go here
        return []

    # ==================== RESULT PROCESSING ====================

    async def _process_search_results(self) -> None:
        """Process and enhance search results"""
        self._logger.debug("Processing search results")
        
        for query, results in self.search_results.items():
            processed = []
            
            for result in results:
                enhanced_result = await self._enhance_result(result)
                if self._should_include_result(enhanced_result):
                    processed.append(enhanced_result)
            
            # Sort by relevance score
            processed.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
            
            self.processed_results[query] = processed
        
        # Build knowledge base
        await self._build_knowledge_base()

    async def _enhance_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance search result with additional metadata"""
        enhanced = result.copy()
        
        # Add domain analysis
        if 'url' in result:
            parsed_url = urlparse(result['url'])
            enhanced['domain'] = parsed_url.netloc
            enhanced['domain_type'] = self._categorize_domain(parsed_url.netloc)
        
        # Add content analysis
        enhanced['content_length'] = len(result.get('snippet', ''))
        enhanced['has_code'] = 'code' in result.get('snippet', '').lower()
        enhanced['is_official'] = self._is_official_source(enhanced.get('domain', ''))
        
        # Calculate final relevance score
        enhanced['final_relevance'] = self._calculate_relevance_score(enhanced)
        
        return enhanced

    def _should_include_result(self, result: Dict[str, Any]) -> bool:
        """Determine if result should be included"""
        domain = result.get('domain', '')
        
        # Check allowed domains
        if self.allowed_domains and domain not in self.allowed_domains:
            return False
        
        # Check blocked domains
        if domain in self.blocked_domains:
            return False
        
        # Check minimum relevance score
        min_score = self.search_config.get('min_relevance_score', 0.5)
        if result.get('final_relevance', 0) < min_score:
            return False
        
        return True

    async def _build_knowledge_base(self) -> None:
        """Build structured knowledge base from search results"""
        knowledge_categories = {
            'documentation': [],
            'tutorials': [],
            'examples': [],
            'community': [],
            'official': []
        }
        
        for query, results in self.processed_results.items():
            for result in results:
                content_type = result.get('content_type', 'other')
                
                if content_type in knowledge_categories:
                    knowledge_categories[content_type].append({
                        'query': query,
                        'title': result.get('title', ''),
                        'url': result.get('url', ''),
                        'snippet': result.get('snippet', ''),
                        'relevance': result.get('final_relevance', 0),
                        'domain': result.get('domain', ''),
                        'is_official': result.get('is_official', False)
                    })
        
        # Sort categories by relevance
        for category in knowledge_categories:
            knowledge_categories[category].sort(
                key=lambda x: x['relevance'], 
                reverse=True
            )
        
        self.knowledge_base = knowledge_categories

    # ==================== UTILITY METHODS ====================

    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate results based on URL"""
        seen_urls = set()
        unique_results = []
        
        for result in results:
            url = result.get('url', '')
            if url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)
        
        return unique_results

    def _categorize_domain(self, domain: str) -> str:
        """Categorize domain type"""
        if any(term in domain for term in ['docs', 'documentation']):
            return 'documentation'
        elif any(term in domain for term in ['github', 'gitlab', 'bitbucket']):
            return 'code_repository'
        elif 'stackoverflow' in domain:
            return 'community'
        elif any(term in domain for term in ['tutorial', 'guide', 'learn']):
            return 'tutorial'
        else:
            return 'general'

    def _is_official_source(self, domain: str) -> bool:
        """Check if domain is an official source"""
        official_indicators = [
            'docs.python.org',
            'docs.microsoft.com', 
            'developer.mozilla.org',
            'docs.github.com',
            'reactjs.org'
        ]
        
        return any(indicator in domain for indicator in official_indicators)

    def _calculate_relevance_score(self, result: Dict[str, Any]) -> float:
        """Calculate final relevance score"""
        base_score = result.get('relevance_score', 0.5)
        
        # Boost for official sources
        if result.get('is_official', False):
            base_score += 0.2
        
        # Boost for documentation
        if result.get('content_type') == 'documentation':
            base_score += 0.1
        
        # Penalty for very short snippets
        if result.get('content_length', 0) < 50:
            base_score -= 0.1
        
        return min(1.0, max(0.0, base_score))

    async def _get_cached_results(self, query: str) -> Optional[List[Dict[str, Any]]]:
        """Get cached search results"""
        # Simple in-memory caching implementation
        # In production, this would use Redis or similar
        cache_key = f"search_cache_{hash(query)}"
        
        if hasattr(self, '_cache') and cache_key in self._cache:
            cached_data = self._cache[cache_key]
            if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                return cached_data['results']
        
        return None

    async def _cache_results(self, query: str, results: List[Dict[str, Any]]) -> None:
        """Cache search results"""
        if not hasattr(self, '_cache'):
            self._cache = {}
        
        cache_key = f"search_cache_{hash(query)}"
        self._cache[cache_key] = {
            'timestamp': datetime.now(),
            'results': results
        }

    async def _test_search_connectivity(self) -> bool:
        """Test search connectivity"""
        try:
            # Perform a simple test search
            test_results = await self._search_web("test query")
            return len(test_results) >= 0  # Allow empty results
        except Exception:
            return False

    # ==================== PUBLIC METHODS ====================

    def _get_top_sources(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top sources across all queries"""
        all_results = []
        
        for results in self.processed_results.values():
            all_results.extend(results)
        
        # Sort by relevance and limit
        all_results.sort(key=lambda x: x.get('final_relevance', 0), reverse=True)
        return all_results[:limit]

    def _extract_key_knowledge(self) -> Dict[str, List[str]]:
        """Extract key knowledge points from search results"""
        knowledge_points = {
            'key_concepts': [],
            'best_practices': [],
            'common_patterns': [],
            'potential_issues': []
        }
        
        for results in self.processed_results.values():
            for result in results:
                snippet = result.get('snippet', '').lower()
                
                # Extract concepts (simplified implementation)
                if 'concept' in snippet or 'principle' in snippet:
                    knowledge_points['key_concepts'].append(result.get('title', ''))
                
                if 'best practice' in snippet or 'recommendation' in snippet:
                    knowledge_points['best_practices'].append(result.get('title', ''))
                
                if 'pattern' in snippet or 'approach' in snippet:
                    knowledge_points['common_patterns'].append(result.get('title', ''))
                
                if 'issue' in snippet or 'problem' in snippet or 'error' in snippet:
                    knowledge_points['potential_issues'].append(result.get('title', ''))
        
        # Limit and deduplicate
        for category in knowledge_points:
            knowledge_points[category] = list(set(knowledge_points[category]))[:5]
        
        return knowledge_points

    def _get_unique_domains(self) -> Set[str]:
        """Get unique domains from all results"""
        domains = set()
        
        for results in self.processed_results.values():
            for result in results:
                domain = result.get('domain')
                if domain:
                    domains.add(domain)
        
        return domains

    def get_search_summary(self) -> Dict[str, Any]:
        """Get comprehensive search summary"""
        return {
            "context_id": self.context_id,
            "queries": self.search_queries,
            "providers": self.search_providers,
            "statistics": self.search_stats,
            "total_results": sum(len(results) for results in self.processed_results.values()),
            "unique_domains": len(self._get_unique_domains()),
            "knowledge_categories": {k: len(v) for k, v in self.knowledge_base.items()},
            "cache_duration_hours": self.cache_duration.total_seconds() / 3600,
            "last_search": self.last_updated.isoformat()
        }

    def get_results_for_query(self, query: str) -> List[Dict[str, Any]]:
        """Get processed results for specific query"""
        return self.processed_results.get(query, [])

    def get_all_results(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get all processed results"""
        return self.processed_results.copy()


# Register WebSearchContext with Factory
ContextFactory.register_context_class(ContextType.WEB_SEARCH, WebSearchContext)