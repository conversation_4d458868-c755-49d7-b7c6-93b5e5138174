"""
Base Agent Abstract Class

Defines the abstract interface for all code review agents in the multi-agent system.
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, List, Optional
import asyncio
import logging
from datetime import datetime

from .models import AgentR<PERSON>ult, AgentExecutionStatus
from ..services.claude_service import Claude<PERSON>er<PERSON>
from ..config.settings import Settings


logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """Abstract base class for all code review agents."""
    
    def __init__(
        self,
        agent_id: str,
        agent_type: str,
        claude_service: ClaudeService,
        settings: Settings,
        timeout_seconds: int = 300
    ):
        """
        Initialize the base agent.
        
        Args:
            agent_id: Unique identifier for this agent instance
            agent_type: Type/category of this agent
            claude_service: Claude Code SDK service instance
            settings: Application settings
            timeout_seconds: Maximum execution time before timeout
        """
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.claude_service = claude_service
        self.settings = settings
        self.timeout_seconds = timeout_seconds
        
        self.logger = logging.getLogger(f"{__name__}.{agent_type}")
        
        # Execution state
        self._current_result: Optional[AgentResult] = None
        self._is_running = False
        self._start_time: Optional[datetime] = None
    
    @abstractmethod
    async def analyze(self, context: Dict[str, Any]) -> AgentResult:
        """
        Main analysis method that each agent must implement.
        
        Args:
            context: Analysis context containing code, configuration, etc.
            
        Returns:
            AgentResult with analysis findings
        """
        pass
    
    @abstractmethod
    def get_prompt(self, context: Dict[str, Any]) -> str:
        """
        Get the agent-specific prompt template.
        
        Args:
            context: Analysis context
            
        Returns:
            Formatted prompt string for Claude
        """
        pass
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """
        Get the system prompt for this agent type.
        
        Returns:
            System prompt string
        """
        pass
    
    async def execute(self, context: Dict[str, Any]) -> AgentResult:
        """
        Execute the agent with timeout and error handling.
        
        Args:
            context: Analysis context
            
        Returns:
            AgentResult with execution outcome
        """
        result = AgentResult(
            agent_id=self.agent_id,
            agent_type=self.agent_type,
            context_data=context.copy()
        )
        
        self._current_result = result
        self._is_running = True
        self._start_time = datetime.utcnow()
        
        try:
            result.mark_started()
            self.logger.info(f"Starting {self.agent_type} agent execution")
            
            # Execute with timeout
            analysis_result = await asyncio.wait_for(
                self.analyze(context),
                timeout=self.timeout_seconds
            )
            
            # Merge results if analyze() returns a different AgentResult
            if isinstance(analysis_result, AgentResult):
                result = analysis_result
                if not result.started_at:
                    result.started_at = self._start_time
            
            result.mark_completed(success=True)
            self.logger.info(f"Completed {self.agent_type} agent execution successfully")
            
        except asyncio.TimeoutError:
            result.mark_timeout()
            self.logger.error(f"{self.agent_type} agent execution timed out after {self.timeout_seconds}s")
            
        except Exception as e:
            error_msg = f"Agent execution failed: {str(e)}"
            result.mark_failed(
                error_message=error_msg,
                error_details={
                    "exception_type": type(e).__name__,
                    "exception_message": str(e)
                }
            )
            result.exception_type = type(e).__name__
            self.logger.error(f"{self.agent_type} agent failed: {error_msg}", exc_info=True)
            
        finally:
            self._is_running = False
            self._current_result = None
        
        return result
    
    async def execute_claude_query(
        self,
        prompt: str,
        context: Dict[str, Any],
        max_turns: int = 5
    ) -> Dict[str, Any]:
        """
        Execute a Claude Code SDK query with proper configuration.
        
        Args:
            prompt: The prompt to send to Claude
            context: Execution context including working directory
            max_turns: Maximum conversation turns
            
        Returns:
            Claude's response as dictionary containing the query result
        """
        try:
            # Get working directory from context
            working_path = Path(context.get("working_path", "."))
            
            # Add progress update
            if self._current_result:
                self._current_result.add_progress_update(
                    f"Executing Claude query for {self.agent_type}"
                )
            
            # Execute the query using Claude service
            response = await self.claude_service.execute_agent_query(
                prompt=prompt,
                system_prompt=self.get_system_prompt(),
                working_directory=working_path,
                max_turns=max_turns,
                agent_type=self.agent_type
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"Claude query failed for {self.agent_type}: {str(e)}")
            raise
    
    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this agent."""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "timeout_seconds": self.timeout_seconds,
            "is_running": self._is_running,
            "start_time": self._start_time.isoformat() if self._start_time else None
        }
    
    def is_running(self) -> bool:
        """Check if the agent is currently running."""
        return self._is_running
    
    def get_current_result(self) -> Optional[AgentResult]:
        """Get the current execution result if running."""
        return self._current_result
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check for this agent.
        
        Returns:
            Health status information
        """
        return {
            "agent_type": self.agent_type,
            "agent_id": self.agent_id,
            "status": "healthy",
            "is_running": self._is_running,
            "claude_service_healthy": await self.claude_service.health_check(),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def _extract_context_value(self, context: Dict[str, Any], key: str, default: Any = None) -> Any:
        """
        Safely extract a value from the context.
        
        Args:
            context: Context dictionary
            key: Key to extract
            default: Default value if key not found
            
        Returns:
            Extracted value or default
        """
        return context.get(key, default)
    
    def _validate_context(self, context: Dict[str, Any], required_keys: List[str]) -> None:
        """
        Validate that required keys are present in context.
        
        Args:
            context: Context to validate
            required_keys: List of required keys
            
        Raises:
            ValueError: If required keys are missing
        """
        missing_keys = [key for key in required_keys if key not in context]
        if missing_keys:
            raise ValueError(f"Missing required context keys: {missing_keys}")
    
    def _format_progress_message(self, message: str, details: Optional[str] = None) -> str:
        """
        Format a progress message with agent information.
        
        Args:
            message: Base message
            details: Optional additional details
            
        Returns:
            Formatted message
        """
        formatted = f"[{self.agent_type}] {message}"
        if details:
            formatted += f" - {details}"
        return formatted
    
    def configure(self, config: Dict[str, Any]) -> None:
        """
        Configure the agent with additional settings.
        Override in subclasses if specialized configuration is needed.
        
        Args:
            config: Configuration dictionary with agent-specific settings
        """
        # Base implementation does nothing - subclasses can override
        pass
    
    async def cleanup(self) -> None:
        """
        Cleanup resources after agent execution.
        Override in subclasses if needed.
        """
        pass
    
    def __str__(self) -> str:
        """String representation of the agent."""
        return f"{self.__class__.__name__}(id={self.agent_id}, type={self.agent_type})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the agent."""
        return (
            f"{self.__class__.__name__}("
            f"agent_id='{self.agent_id}', "
            f"agent_type='{self.agent_type}', "
            f"timeout={self.timeout_seconds}s, "
            f"running={self._is_running}"
            f")"
        )