"""
Architectural Agent Strategy
Implements the ArchitecturalAgent for System Design and Architecture Review.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from ..base_agent import BaseAgent
from ..models.agent_result import AgentResult, AgentExecutionStatus

logger = logging.getLogger(__name__)


class ArchitecturalAgent(BaseAgent):
    """
    Architectural Agent für System Design und Architecture Review.
    
    Responsibilities:
    - Design Patterns Usage Analysis
    - System Architecture Review Logic
    - Scalability Considerations Assessment
    - Technical Debt Identification
    - Component Coupling Analysis
    - Layer Architecture Validation
    """
    
    def __init__(self, agent_id: str, agent_type: str, claude_service, settings, timeout_seconds: int = 300, config: Optional[Dict[str, Any]] = None):
        """Initialize Architectural Agent."""
        super().__init__(agent_id, agent_type, claude_service, settings, timeout_seconds)
        self.agent_type = "architectural"
        self.display_name = "Architectural Agent"
        self.description = "Analyzes system architecture, design patterns, scalability, technical debt and component relationships"
        
        # Architecture analysis specific configuration
        self.arch_config = config.get("architectural", {}) if config else {}
        self.pattern_analysis = self.arch_config.get("design_patterns", True)
        self.scalability_assessment = self.arch_config.get("scalability", True)
        self.technical_debt_tracking = self.arch_config.get("technical_debt", True)
        self.coupling_analysis = self.arch_config.get("coupling_analysis", True)
        self.layer_validation = self.arch_config.get("layer_validation", True)
        
    def get_system_prompt(self) -> str:
        """Get the system prompt for architectural analysis."""
        return """You are a specialized Architectural Analysis Agent for code review analysis.

Your primary responsibilities:
- Analyze system architecture and design patterns
- Evaluate scalability and technical debt
- Review component coupling and layer architecture
- Assess design pattern usage and system structure
- Identify architectural violations and improvements
- Provide architectural best practices recommendations

Focus on providing specific, actionable architectural findings with exact file locations and clear explanations of design decisions and improvements."""

    def get_prompt(self, context: Dict[str, Any]) -> str:
        """Generate dynamic prompt for architectural analysis."""
        branch_name = context.get("branch_name", "unknown")
        working_directory = context.get("working_directory", ".")
        
        prompt = f"""
# Architectural Analysis Request

**Branch:** {branch_name}
**Working Directory:** {working_directory}

## Analysis Focus Areas:

1. **System Architecture & Design Patterns**
   - Layered architecture implementation
   - Design pattern usage (Factory, Strategy, Observer, etc.)
   - Clean architecture principles
   - Microservices vs monolithic patterns

2. **Component Coupling & Dependencies**
   - Tight vs loose coupling analysis
   - Dependency injection usage
   - Interface segregation
   - Component boundary definitions

3. **Scalability & Performance Architecture**
   - Horizontal and vertical scalability considerations
   - Caching strategies
   - Database architecture patterns
   - Load balancing and distribution

4. **Technical Debt & Maintenance**
   - Legacy code identification
   - Over-engineering vs under-engineering
   - Configuration management
   - Documentation debt

5. **API Design & Integration**
   - RESTful design principles
   - API versioning strategies
   - Service integration patterns
   - Error handling architecture

## Instructions:
1. Analyze the code changes in the working directory
2. Focus on architectural decisions and system design
3. Provide specific recommendations with exact file locations
4. Explain the impact of architectural choices
5. Suggest concrete improvements and best practices

Please perform a thorough architectural analysis of the code changes.
"""
        return prompt.strip()

    async def analyze(self, context: Dict[str, Any]) -> AgentResult:
        """
        Execute architectural analysis on the provided code context.
        
        Args:
            context: Analysis context containing changed files, working directory, etc.
            
        Returns:
            AgentResult with architectural analysis findings
        """
        try:
            logger.info(f"Starting architectural analysis for agent {self.agent_id}")
            
            # Extract architectural context
            arch_context = await self._extract_architectural_context(context)
            
            # Get architectural analysis prompt
            prompt = await self._build_architectural_prompt(arch_context)
            
            # Execute Claude Code SDK query for architectural analysis
            analysis_result = await self.execute_claude_query(
                prompt=prompt,
                context=context,
                max_turns=15  # Architecture analysis needs comprehensive investigation
            )
            
            # Process and structure the architectural result
            structured_result = await self._process_architectural_analysis(analysis_result, arch_context)
            
            logger.info(f"Architectural analysis completed for agent {self.agent_id}")
            
            result = AgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                status=AgentExecutionStatus.SUCCESS,
                success=True
            )
            result.set_result_data({"analysis": structured_result})
            result.context_data = {
                "files_analyzed": len(arch_context.get("changed_files", [])),
                "architecture_categories_checked": [
                    "design_patterns", "system_architecture", "scalability", "technical_debt",
                    "component_coupling", "layer_validation", "dependency_management"
                ],
                "pattern_analysis": self.pattern_analysis,
                "scalability_assessment": self.scalability_assessment,
                "technical_debt_tracking": self.technical_debt_tracking
            }
            return result
            
        except Exception as e:
            logger.error(f"Architectural analysis failed for agent {self.agent_id}: {str(e)}")
            result = AgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                status=AgentExecutionStatus.FAILED,
                success=False,
                error_message=str(e)
            )
            result.set_result_data({"error": f"Architectural analysis failed: {str(e)}"})
            return result
    
    async def _extract_architectural_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract architectural analysis relevant context from the general context."""
        arch_context = {
            "changed_files": context.get("changed_files", []),
            "working_path": context.get("working_path", "."),
            "branch_name": context.get("branch_name", ""),
            "ticket_id": context.get("ticket_id", ""),
            "jira_info": context.get("jira_info", {}),
        }
        
        # Filter for architecture-relevant file types
        relevant_files = []
        arch_extensions = {'.py', '.js', '.ts', '.tsx', '.jsx', '.java', '.cpp', '.c', '.cs', '.rb', '.go', '.rs', '.php', '.scala', '.kt'}
        
        for file_path in arch_context["changed_files"]:
            if Path(file_path).suffix.lower() in arch_extensions:
                relevant_files.append(file_path)
        
        arch_context["architecture_files"] = relevant_files
        
        # Identify architectural component files
        component_files = []
        component_patterns = {
            'component', 'service', 'controller', 'middleware', 'handler', 'processor',
            'manager', 'factory', 'builder', 'adapter', 'facade', 'proxy', 'decorator'
        }
        
        for file_path in relevant_files:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in component_patterns):
                component_files.append(file_path)
        
        arch_context["component_files"] = component_files
        
        # Identify configuration and infrastructure files
        infrastructure_files = []
        infra_patterns = {
            'config', 'setting', 'docker', 'kubernetes', 'helm', 'terraform',
            'deployment', 'pipeline', 'workflow', 'makefile', 'dockerfile'
        }
        
        for file_path in arch_context["changed_files"]:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in infra_patterns):
                infrastructure_files.append(file_path)
        
        arch_context["infrastructure_files"] = infrastructure_files
        
        # Identify API and interface files
        api_files = []
        api_patterns = {
            'api', 'endpoint', 'route', 'interface', 'contract', 'schema',
            'openapi', 'swagger', 'graphql', 'grpc', 'proto'
        }
        
        for file_path in relevant_files:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in api_patterns):
                api_files.append(file_path)
        
        arch_context["api_files"] = api_files
        
        # Identify data layer files
        data_files = []
        data_patterns = {
            'model', 'entity', 'repository', 'dao', 'database', 'migration',
            'schema', 'orm', 'query', 'store', 'persistence'
        }
        
        for file_path in relevant_files:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in data_patterns):
                data_files.append(file_path)
        
        arch_context["data_files"] = data_files
        
        return arch_context
    
    async def _build_architectural_prompt(self, arch_context: Dict[str, Any]) -> str:
        """Build the architectural analysis prompt based on context."""
        
        # Architectural analysis prompt (German)
        base_prompt = """# Architectural Analysis Review Prompt

Du führst eine **tiefgreifende Architektur-Analyse** durch mit Fokus auf System Design, Design Patterns, Scalability und Technical Debt.

**⚠️ WICHTIGE ANWEISUNG: Verwende das Read tool um Code zu analysieren und zeige IMMER konkrete Code-Snippets mit exakten Datei:Zeile Referenzen!**

**ANALYSE-VORGEHEN:**
1. **Read verwenden:** Lies alle relevanten Dateien mit dem Read tool
2. **System View:** Verstehe die Gesamtarchitektur durch mehrere Dateien
3. **Pattern Recognition:** Identifiziere verwendete und fehlende Design Patterns
4. **Dependency Analysis:** Analysiere Component-Dependencies und Coupling

## 🎯 REVIEW-FOKUS: SYSTEM ARCHITECTURE & DESIGN

### HAUPTZIEL
Systematische Analyse der System-Architektur, Design Patterns, Scalability, Technical Debt und Component-Relationships mit Fokus auf langfristige Maintainability.

## 🔍 ARCHITEKTUR-ANALYSE-KATEGORIEN

### 1. 🏗️ SYSTEM ARCHITECTURE & DESIGN PATTERNS
**Schwerpunkt**: Architectural Patterns und System Design

- **Layered Architecture**: Presentation, Business, Data Layer Separation
- **Clean Architecture**: Dependency Rule, Use Cases, Entities
- **Hexagonal Architecture**: Ports and Adapters Pattern
- **Microservices Architecture**: Service Boundaries, Communication Patterns
- **Event-Driven Architecture**: Event Sourcing, CQRS, Pub/Sub
- **MVC/MVP/MVVM**: UI Architecture Patterns
- **Repository Pattern**: Data Access Abstraction
- **Factory Pattern**: Object Creation, Dependency Injection
- **Strategy Pattern**: Algorithm Variations, Pluggable Components
- **Observer Pattern**: Event Handling, Loose Coupling
- **Decorator Pattern**: Feature Extension, Aspect-Oriented Programming
- **Facade Pattern**: Complex Subsystem Simplification

### 2. 🧩 COMPONENT COUPLING & COHESION
**Schwerpunkt**: Module Dependencies und Component Design

- **Tight Coupling**: Direct dependencies, Hard-coded references
- **Loose Coupling**: Interface-based dependencies, Dependency Injection
- **High Cohesion**: Related functionality grouped together
- **Low Cohesion**: Unrelated functionality in same component
- **Circular Dependencies**: Module dependency cycles
- **Dependency Direction**: High-level vs low-level dependencies
- **Interface Segregation**: Multiple specific interfaces vs fat interfaces
- **Component Boundaries**: Clear separation of concerns
- **Cross-Cutting Concerns**: Logging, Security, Caching across layers
- **Plugin Architecture**: Extensible component system

### 3. 📈 SCALABILITY & PERFORMANCE ARCHITECTURE
**Schwerpunkt**: System Scalability und Performance Design

- **Horizontal Scalability**: Load distribution, Stateless design
- **Vertical Scalability**: Resource utilization, Performance optimization
- **Database Scalability**: Sharding, Read replicas, Connection pooling
- **Caching Strategy**: Multi-level caching, Cache invalidation
- **Load Balancing**: Traffic distribution, Failover strategies
- **Asynchronous Processing**: Message queues, Background jobs
- **Resource Management**: Memory usage, Connection management
- **Data Partitioning**: Sharding strategies, Data locality
- **CDN Integration**: Static asset delivery, Geographic distribution
- **Auto-scaling**: Dynamic resource allocation, Performance monitoring

### 4. 🔧 TECHNICAL DEBT & MAINTENANCE
**Schwerpunkt**: Technical Debt Assessment und Refactoring Opportunities

- **Legacy Code**: Outdated patterns, Deprecated dependencies
- **Code Duplication**: Repeated functionality, Copy-paste programming
- **Over-Engineering**: Unnecessary complexity, Premature optimization
- **Under-Engineering**: Missing abstractions, Inadequate error handling
- **Configuration Debt**: Hard-coded values, Environment-specific code
- **Documentation Debt**: Missing documentation, Outdated comments
- **Test Debt**: Missing tests, Inadequate test coverage
- **Dependency Debt**: Outdated libraries, Security vulnerabilities
- **Performance Debt**: Known performance issues, Monitoring gaps
- **Security Debt**: Known security issues, Missing security measures

### 5. 🌐 API DESIGN & INTEGRATION ARCHITECTURE
**Schwerpunkt**: API Design und External Integration

- **RESTful Design**: Resource-based URLs, HTTP methods, Status codes
- **GraphQL Architecture**: Schema design, Query optimization, N+1 problems
- **API Versioning**: Backward compatibility, Breaking changes
- **Error Handling**: Consistent error responses, Error codes
- **Authentication**: OAuth, JWT, API keys, Security patterns
- **Rate Limiting**: Throttling, Quotas, Fair usage
- **API Documentation**: OpenAPI, Swagger, API contracts
- **Service Integration**: External APIs, Fallback strategies, Circuit breakers
- **Message Formats**: JSON, Protocol Buffers, Message schemas
- **Real-time Communication**: WebSockets, Server-Sent Events, Polling

### 6. 💾 DATA ARCHITECTURE & PERSISTENCE
**Schwerpunkt**: Data Layer Design und Storage Patterns

- **Database Design**: Normalization, Denormalization, Schema design
- **ORM Usage**: Query efficiency, N+1 problems, Raw queries
- **Data Access Patterns**: Repository, Active Record, Data Mapper
- **Transaction Management**: ACID properties, Distributed transactions
- **Data Consistency**: Eventual consistency, Strong consistency
- **Data Migration**: Schema changes, Data transformation
- **Backup Strategy**: Data backup, Recovery procedures
- **Data Security**: Encryption at rest, Access control
- **Performance Optimization**: Indexing, Query optimization
- **Multi-tenancy**: Data isolation, Shared vs separate schemas

## 📊 DETAILLIERTES OUTPUT FORMAT

### ARCHITECTURE DASHBOARD
```markdown
## 🏗️ ARCHITECTURAL ANALYSIS EXECUTIVE DASHBOARD

**Overall Architecture Quality**: [EXCELLENT/GOOD/FAIR/POOR]
**Design Pattern Usage**: [OPTIMAL/GOOD/INCONSISTENT/ANTI_PATTERNS]
**Scalability Readiness**: [HIGHLY_SCALABLE/SCALABLE/LIMITED/NOT_SCALABLE]
**Technical Debt Level**: [LOW/MEDIUM/HIGH/CRITICAL]

### Architecture Assessment Summary
- 🚨 Critical Issues: X (Must address before production)
- ❌ Major Issues: Y (Should fix before merge)  
- ⚠️ Design Improvements: Z (Address in next sprint)
- 💡 Optimizations: W (Future enhancements)

### Architecture Categories
- 🏗️ System Design: X
- 🧩 Component Coupling: Y
- 📈 Scalability: Z
- 🔧 Technical Debt: W
- 🌐 API Design: V
- 💾 Data Architecture: U
```

### CRITICAL ARCHITECTURE ISSUES SECTION
```markdown
## 🚨 CRITICAL ARCHITECTURAL ISSUES

### 1. Monolithic Anti-Pattern with Tight Coupling
**Files**: `src/services/ApplicationService.ts:1-892` + Multiple related files
**Severity**: CRITICAL
**Architecture Issue**: God Service + Tight Coupling
**Scalability Impact**: HIGH
**Technical Debt**: CRITICAL

**Problematic Architecture**:
```typescript
// ApplicationService.ts - 892 lines of tightly coupled code
class ApplicationService {
  constructor() {
    // Direct instantiation - tight coupling!
    this.userService = new UserService();
    this.paymentService = new PaymentService();
    this.emailService = new EmailService();
    this.inventoryService = new InventoryService();
    this.notificationService = new NotificationService();
    this.auditService = new AuditService();
    this.reportingService = new ReportingService();
  }
  
  // User management mixed with business logic
  async createUser(userData) {
    const user = await this.userService.create(userData);
    await this.emailService.sendWelcome(user.email);
    await this.auditService.log('user_created', user.id);
    await this.notificationService.notify('admin', 'new_user');
    return user;
  }
  
  // Payment processing mixed with inventory
  async processOrder(orderData) {
    const order = await this.createOrder(orderData);
    await this.inventoryService.reserve(order.items);
    const payment = await this.paymentService.process(order.total);
    await this.emailService.sendOrderConfirmation(order.id);
    await this.reportingService.updateSales(order.total);
    return order;
  }
  
  // ... 50+ more methods mixing different concerns
}
```

**Architectural Problems**:
1. **Single Responsibility Violation**: One class handles 7+ different domains
2. **Tight Coupling**: Direct instantiation prevents testing and flexibility
3. **Scalability Issues**: Cannot scale individual services independently
4. **Maintainability**: Changes in one area affect entire system
5. **Testing Complexity**: Impossible to unit test individual components

**Refactored Clean Architecture**:
```typescript
// Domain Layer - Business Entities
interface User {
  id: string;
  email: string;
  name: string;
}

interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  total: number;
}

// Use Case Layer - Application Services
interface UserRepository {
  create(user: User): Promise<User>;
  findById(id: string): Promise<User>;
}

interface EmailService {
  sendWelcome(email: string): Promise<void>;
  sendOrderConfirmation(orderId: string): Promise<void>;
}

class CreateUserUseCase {
  constructor(
    private userRepository: UserRepository,
    private emailService: EmailService,
    private auditService: AuditService
  ) {}
  
  async execute(userData: CreateUserRequest): Promise<User> {
    const user = await this.userRepository.create(userData);
    await this.emailService.sendWelcome(user.email);
    await this.auditService.log('user_created', user.id);
    return user;
  }
}

class ProcessOrderUseCase {
  constructor(
    private orderRepository: OrderRepository,
    private inventoryService: InventoryService,
    private paymentService: PaymentService,
    private emailService: EmailService
  ) {}
  
  async execute(orderData: ProcessOrderRequest): Promise<Order> {
    const order = await this.orderRepository.create(orderData);
    
    // Check inventory availability
    const inventoryCheck = await this.inventoryService.checkAvailability(order.items);
    if (!inventoryCheck.available) {
      throw new InventoryNotAvailableError();
    }
    
    // Reserve items
    await this.inventoryService.reserve(order.items);
    
    try {
      // Process payment
      const payment = await this.paymentService.process(order.total);
      
      // Update order status
      order.status = 'paid';
      await this.orderRepository.update(order);
      
      // Send confirmation
      await this.emailService.sendOrderConfirmation(order.id);
      
      return order;
    } catch (error) {
      // Rollback inventory reservation
      await this.inventoryService.release(order.items);
      throw error;
    }
  }
}

// Interface Layer - Controllers
class UserController {
  constructor(private createUserUseCase: CreateUserUseCase) {}
  
  async createUser(req: Request, res: Response) {
    try {
      const user = await this.createUserUseCase.execute(req.body);
      res.status(201).json(user);
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }
}

// Infrastructure Layer - Dependency Injection
class DIContainer {
  private services = new Map();
  
  register<T>(key: string, factory: () => T): void {
    this.services.set(key, factory);
  }
  
  resolve<T>(key: string): T {
    const factory = this.services.get(key);
    if (!factory) throw new Error(`Service ${key} not registered`);
    return factory();
  }
}

// Registration
const container = new DIContainer();
container.register('userRepository', () => new PostgresUserRepository());
container.register('emailService', () => new SendGridEmailService());
container.register('createUserUseCase', () => new CreateUserUseCase(
  container.resolve('userRepository'),
  container.resolve('emailService'),
  container.resolve('auditService')
));
```

**Architecture Benefits**:
- ✅ **Single Responsibility**: Each use case has one responsibility
- ✅ **Loose Coupling**: Dependency injection enables testing and flexibility
- ✅ **Scalability**: Individual services can be scaled independently
- ✅ **Testability**: Each component can be unit tested in isolation
- ✅ **Maintainability**: Changes are isolated to specific domains

---

### 2. Database Layer Violation (N+1 Query Problem)
**File**: `src/repositories/orderRepository.ts:89-134`
**Severity**: CRITICAL
**Architecture Issue**: Data Access Anti-Pattern
**Performance Impact**: SEVERE
**Scalability Risk**: HIGH

**Problematic Data Access Pattern**:
```typescript
class OrderRepository {
  async getOrdersWithDetails(): Promise<OrderWithDetails[]> {
    // Initial query - loads all orders
    const orders = await this.db.query('SELECT * FROM orders');
    
    // N+1 problem - separate query for each order!
    for (const order of orders) {
      order.customer = await this.db.query(
        'SELECT * FROM customers WHERE id = ?', 
        [order.customer_id]
      );
      
      order.items = await this.db.query(
        'SELECT * FROM order_items WHERE order_id = ?', 
        [order.id]
      );
      
      // Even worse - query for each item's product details
      for (const item of order.items) {
        item.product = await this.db.query(
          'SELECT * FROM products WHERE id = ?', 
          [item.product_id]
        );
      }
    }
    
    return orders;
  }
}
```

**Performance Impact Analysis**:
- 10 orders → 1 + 10 + 10 + 50 = 71 queries
- 100 orders → 1 + 100 + 100 + 500 = 701 queries  
- 1000 orders → 1 + 1000 + 1000 + 5000 = 7001 queries

**Optimized Data Access Architecture**:
```typescript
class OptimizedOrderRepository {
  async getOrdersWithDetails(): Promise<OrderWithDetails[]> {
    // Single optimized query with JOINs
    const query = `
      SELECT 
        o.id as order_id,
        o.total as order_total,
        o.created_at as order_date,
        c.id as customer_id,
        c.name as customer_name,
        c.email as customer_email,
        oi.id as item_id,
        oi.quantity,
        oi.price as item_price,
        p.id as product_id,
        p.name as product_name,
        p.description as product_description
      FROM orders o
      JOIN customers c ON o.customer_id = c.id
      JOIN order_items oi ON o.id = oi.order_id
      JOIN products p ON oi.product_id = p.id
      ORDER BY o.id, oi.id
    `;
    
    const rows = await this.db.query(query);
    
    // Transform flat result into nested structure
    return this.transformRowsToOrders(rows);
  }
  
  private transformRowsToOrders(rows: any[]): OrderWithDetails[] {
    const ordersMap = new Map<string, OrderWithDetails>();
    
    for (const row of rows) {
      let order = ordersMap.get(row.order_id);
      
      if (!order) {
        order = {
          id: row.order_id,
          total: row.order_total,
          createdAt: row.order_date,
          customer: {
            id: row.customer_id,
            name: row.customer_name,
            email: row.customer_email
          },
          items: []
        };
        ordersMap.set(row.order_id, order);
      }
      
      order.items.push({
        id: row.item_id,
        quantity: row.quantity,
        price: row.item_price,
        product: {
          id: row.product_id,
          name: row.product_name,
          description: row.product_description
        }
      });
    }
    
    return Array.from(ordersMap.values());
  }
  
  // Alternative: Using ORM with eager loading
  async getOrdersWithDetailsORM(): Promise<Order[]> {
    return await this.orderModel.findAll({
      include: [
        {
          model: Customer,
          as: 'customer'
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        }
      ]
    });
  }
}
```

**Performance Improvement**: 7001 queries → 1 query (99.99% reduction)

---

### 3. Missing Circuit Breaker Pattern for External Services
**File**: `src/services/paymentService.ts:45-89`
**Severity**: CRITICAL
**Architecture Issue**: Missing Resilience Patterns
**Reliability Risk**: HIGH
**Cascading Failure Risk**: CRITICAL

**Fragile External Service Integration**:
```typescript
class PaymentService {
  async processPayment(amount: number, cardToken: string): Promise<PaymentResult> {
    try {
      // Direct call to external service - no resilience!
      const response = await fetch('https://payment-gateway.com/charge', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${this.apiKey}` },
        body: JSON.stringify({ amount, cardToken }),
        timeout: 30000 // 30 second timeout
      });
      
      if (!response.ok) {
        throw new Error('Payment failed');
      }
      
      return await response.json();
    } catch (error) {
      // Simple error handling - no resilience strategy
      throw new PaymentError(`Payment processing failed: ${error.message}`);
    }
  }
}
```

**Problems**:
1. **No Circuit Breaker**: Continues trying failed service
2. **No Retry Logic**: Single failure causes immediate error
3. **No Fallback**: No alternative when service is down
4. **Cascading Failures**: Service failures propagate through system

**Resilient Architecture with Circuit Breaker**:
```typescript
enum CircuitState {
  CLOSED = 'closed',
  OPEN = 'open', 
  HALF_OPEN = 'half_open'
}

class CircuitBreaker {
  private state = CircuitState.CLOSED;
  private failures = 0;
  private nextAttempt = 0;
  
  constructor(
    private failureThreshold = 5,
    private recoveryTimeout = 60000,
    private monitoringPeriod = 10000
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (Date.now() < this.nextAttempt) {
        throw new CircuitOpenError('Circuit breaker is OPEN');
      }
      this.state = CircuitState.HALF_OPEN;
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = CircuitState.CLOSED;
  }
  
  private onFailure(): void {
    this.failures++;
    if (this.failures >= this.failureThreshold) {
      this.state = CircuitState.OPEN;
      this.nextAttempt = Date.now() + this.recoveryTimeout;
    }
  }
}

class ResilientPaymentService {
  private circuitBreaker = new CircuitBreaker(5, 60000);
  private retryPolicy = new ExponentialBackoffRetry(3, 1000);
  
  async processPayment(amount: number, cardToken: string): Promise<PaymentResult> {
    return await this.circuitBreaker.execute(async () => {
      return await this.retryPolicy.execute(async () => {
        return await this.makePaymentRequest(amount, cardToken);
      });
    });
  }
  
  private async makePaymentRequest(amount: number, cardToken: string): Promise<PaymentResult> {
    const response = await fetch('https://payment-gateway.com/charge', {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${this.apiKey}` },
      body: JSON.stringify({ amount, cardToken }),
      timeout: 10000
    });
    
    if (!response.ok) {
      throw new PaymentGatewayError(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  // Fallback strategy for when primary payment fails
  async processPaymentWithFallback(amount: number, cardToken: string): Promise<PaymentResult> {
    try {
      return await this.processPayment(amount, cardToken);
    } catch (error) {
      if (error instanceof CircuitOpenError) {
        // Try alternative payment processor
        return await this.fallbackPaymentProcessor.processPayment(amount, cardToken);
      }
      throw error;
    }
  }
}

class ExponentialBackoffRetry {
  constructor(
    private maxRetries: number,
    private baseDelay: number
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === this.maxRetries) {
          break;
        }
        
        const delay = this.baseDelay * Math.pow(2, attempt);
        await this.sleep(delay);
      }
    }
    
    throw lastError;
  }
  
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

**Resilience Benefits**:
- ✅ **Circuit Breaker**: Prevents cascading failures
- ✅ **Retry Logic**: Handles transient failures
- ✅ **Fallback Strategy**: Alternative when primary fails
- ✅ **Monitoring**: Track failure rates and recovery
```

### MAJOR ARCHITECTURE ISSUES SECTION
```markdown
## ❌ MAJOR ARCHITECTURAL ISSUES

### 1. Layer Architecture Violation
**Files**: UI components directly accessing database
**Severity**: MAJOR
**Issue**: Presentation layer bypassing business logic
**Fix**: Implement proper layered architecture with clear boundaries

### 2. Missing API Versioning Strategy
**Files**: API endpoint definitions
**Severity**: MAJOR  
**Issue**: No backward compatibility strategy
**Fix**: Implement URL-based or header-based API versioning

### 3. Inadequate Error Handling Architecture
**Files**: Multiple service layers
**Severity**: MAJOR
**Issue**: Inconsistent error handling patterns
**Fix**: Centralized error handling with proper error classification

## 💡 ARCHITECTURAL IMPROVEMENTS & BEST PRACTICES

### Design Pattern Opportunities
1. **Factory Pattern**: For complex object creation
2. **Strategy Pattern**: For algorithm variations
3. **Observer Pattern**: For event-driven architecture
4. **Repository Pattern**: For data access abstraction

### Scalability Enhancements
1. **Horizontal Scaling**: Stateless service design
2. **Caching Strategy**: Multi-level caching implementation
3. **Database Optimization**: Query optimization and indexing
4. **Load Balancing**: Traffic distribution strategies

### Technical Debt Reduction
1. **Dependency Management**: Update outdated libraries
2. **Code Duplication**: Extract common functionality
3. **Configuration Management**: Externalize configuration
4. **Documentation**: Architectural decision records (ADRs)
```

## 🔍 ARCHITEKTUR-ANALYSE-METHODOLOGIE

### System Analysis Framework
1. **Component Analysis**: Individual component design and responsibilities
2. **Integration Analysis**: How components interact and communicate
3. **Data Flow Analysis**: Information flow through system layers
4. **Dependency Analysis**: Component dependencies and coupling levels

### Design Quality Assessment
1. **SOLID Principles**: Single Responsibility, Open/Closed, etc.
2. **Design Patterns**: Appropriate pattern usage and implementation
3. **Separation of Concerns**: Clear boundary definitions
4. **Abstraction Levels**: Consistent abstraction throughout system

### Scalability & Performance Review
1. **Bottleneck Identification**: Performance constraints and limits
2. **Resource Utilization**: Efficient use of system resources
3. **Scaling Strategies**: Horizontal and vertical scaling approaches
4. **Monitoring & Observability**: System health and performance tracking

## ⚠️ WICHTIGE ARCHITEKTUR-ANALYSE-REGELN

1. **System Thinking**: Gesamtsystem verstehen, nicht nur einzelne Components
2. **Pattern Recognition**: Design Patterns korrekt identifizieren und bewerten
3. **Scalability Focus**: Langfristige Scalability und Performance berücksichtigen
4. **Technical Debt**: Technical Debt systematisch identifizieren und priorisieren
5. **Future-Proof**: Architektur-Entscheidungen auf Zukunftsfähigkeit prüfen
6. **Documentation**: Architektur-Entscheidungen dokumentieren und begründen

Führe eine systematische, tiefgreifende Architektur-Analyse durch mit Fokus auf System Design, Scalability und langfristige Maintainability."""

        # Add context-specific information
        files_section = ""
        if arch_context.get("architecture_files"):
            files_section = f"""

## 📁 ARCHITEKTUR-RELEVANTE DATEIEN

Analysiere diese Dateien auf architektonische Patterns und Design:
{chr(10).join(f"- {file}" for file in arch_context["architecture_files"])}
"""

        component_section = ""
        if arch_context.get("component_files") and self.pattern_analysis:
            component_section = f"""

## 🧩 COMPONENT-DATEIEN (DESIGN PATTERNS)

Prüfe diese Component-Dateien auf Design Pattern Usage:
{chr(10).join(f"- {file}" for file in arch_context["component_files"])}
"""

        infrastructure_section = ""
        if arch_context.get("infrastructure_files"):
            infrastructure_section = f"""

## 🏗️ INFRASTRUKTUR-DATEIEN

Analysiere diese Infrastructure/Deployment-Dateien:
{chr(10).join(f"- {file}" for file in arch_context["infrastructure_files"])}
"""

        api_section = ""
        if arch_context.get("api_files"):
            api_section = f"""

## 🌐 API/INTERFACE DATEIEN

Prüfe diese API-Dateien auf Design und Contracts:
{chr(10).join(f"- {file}" for file in arch_context["api_files"])}
"""

        data_section = ""
        if arch_context.get("data_files"):
            data_section = f"""

## 💾 DATA LAYER DATEIEN

Analysiere diese Data-Layer-Dateien auf Persistence Patterns:
{chr(10).join(f"- {file}" for file in arch_context["data_files"])}
"""

        context_section = f"""

## 🎯 ARCHITECTURAL ANALYSIS CONTEXT

**Branch**: {arch_context.get('branch_name', 'unknown')}
**Ticket**: {arch_context.get('ticket_id', 'unknown')}
**Working Directory**: {arch_context.get('working_path', '.')}
**Files to Analyze**: {len(arch_context.get('changed_files', []))} files
**Design Pattern Analysis**: {'✅ Enabled' if self.pattern_analysis else '❌ Disabled'}
**Scalability Assessment**: {'✅ Enabled' if self.scalability_assessment else '❌ Disabled'}
**Technical Debt Tracking**: {'✅ Enabled' if self.technical_debt_tracking else '❌ Disabled'}
**Coupling Analysis**: {'✅ Enabled' if self.coupling_analysis else '❌ Disabled'}
**Layer Validation**: {'✅ Enabled' if self.layer_validation else '❌ Disabled'}
"""

        return base_prompt + context_section + files_section + component_section + infrastructure_section + api_section + data_section

    async def _process_architectural_analysis(self, analysis_result: Dict[str, Any], arch_context: Dict[str, Any]) -> str:
        """Process and structure the architectural analysis result."""
        
        # Add metadata and summary to the result
        metadata_header = f"""# 🏗️ ARCHITECTURAL ANALYSIS REPORT

**Agent**: {self.display_name} ({self.agent_id})
**Analysis Date**: {datetime.now().isoformat()}
**Files Analyzed**: {len(arch_context.get('architecture_files', []))}
**Component Files**: {len(arch_context.get('component_files', []))}
**Infrastructure Files**: {len(arch_context.get('infrastructure_files', []))}
**API Files**: {len(arch_context.get('api_files', []))}
**Data Layer Files**: {len(arch_context.get('data_files', []))}
**Design Pattern Analysis**: {'Enabled' if self.pattern_analysis else 'Disabled'}
**Scalability Assessment**: {'Enabled' if self.scalability_assessment else 'Disabled'}
**Technical Debt Tracking**: {'Enabled' if self.technical_debt_tracking else 'Disabled'}
**Coupling Analysis**: {'Enabled' if self.coupling_analysis else 'Disabled'}
**Layer Validation**: {'Enabled' if self.layer_validation else 'Disabled'}

---

"""
        
        # Extract the actual analysis content from the response
        content = ""
        if isinstance(analysis_result, dict):
            # Try to extract content from various possible keys
            content = (analysis_result.get('content') or 
                      analysis_result.get('response') or 
                      analysis_result.get('result') or 
                      str(analysis_result))
        else:
            content = str(analysis_result)
        
        return metadata_header + content

    def get_agent_info(self) -> Dict[str, Any]:
        """Return agent information for factory registration."""
        return {
            "agent_type": self.agent_type,
            "display_name": self.display_name,
            "description": self.description,
            "capabilities": [
                "System Architecture Analysis",
                "Design Pattern Recognition",
                "Scalability Assessment",
                "Technical Debt Identification",
                "Component Coupling Analysis",
                "Layer Architecture Validation",
                "API Design Review"
            ],
            "supported_file_types": [".py", ".js", ".ts", ".tsx", ".jsx", ".java", ".cpp", ".c", ".cs", ".rb", ".go", ".rs", ".php", ".scala", ".kt"],
            "estimated_execution_time": "15-25 minutes",
            "parallel_compatible": True
        }