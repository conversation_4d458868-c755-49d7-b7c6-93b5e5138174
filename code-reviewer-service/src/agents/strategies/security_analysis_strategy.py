"""
Security Analysis Agent Strategy
Implements the SecurityAgent as an independent, parallelizable unit according to the new agent architecture.
This agent replaces the security part of the old sequential analysis.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from ..base_agent import BaseAgent
from ..models.agent_result import Agent<PERSON><PERSON><PERSON>, AgentExecutionStatus

logger = logging.getLogger(__name__)


class SecurityAnalysisAgent(BaseAgent):
    """
    Security Agent für parallele Sicherheitsanalyse mit spezialisierten Prompts.
    
    Responsibilities:
    - Input Validation vulnerabilities
    - SQL Injection / XSS risks  
    - Authentication/Authorization checks
    - Sensitive Data Exposure
    - Crypto issues and hardcoded secrets
    - Vulnerable dependencies
    """
    
    def __init__(self, agent_id: str, agent_type: str, claude_service, settings, timeout_seconds: int = 300, config: Optional[Dict[str, Any]] = None):
        """Initialize Security Analysis Agent."""
        super().__init__(agent_id, agent_type, claude_service, settings, timeout_seconds)
        self.agent_type = "security_analysis"
        self.display_name = "Security Analysis Agent"
        self.description = "Analyzes code for security vulnerabilities, authentication issues, and data protection concerns"
        
        # Security-specific configuration
        self.security_config = config.get("security", {}) if config else {}
        self.severity_threshold = self.security_config.get("severity_threshold", "medium")
        self.check_dependencies = self.security_config.get("check_dependencies", True)
        
    def get_system_prompt(self) -> str:
        """Get the system prompt for security analysis."""
        return """You are a specialized Security Analysis Agent for code review analysis.

Your primary responsibilities:
- Identify security vulnerabilities and weaknesses
- Analyze authentication and authorization mechanisms
- Detect sensitive data exposure risks
- Review cryptographic implementations
- Assess input validation and injection risks
- Evaluate network and API security measures

Focus on providing specific, actionable security findings with exact code locations and clear remediation steps."""

    def get_prompt(self, context: Dict[str, Any]) -> str:
        """Generate dynamic prompt for security analysis."""
        branch_name = context.get("branch_name", "unknown")
        working_directory = context.get("working_directory", ".")
        
        prompt = f"""
# Security Analysis Request

**Branch:** {branch_name}
**Working Directory:** {working_directory}

## Analysis Focus Areas:

1. **Input Validation & Injection Attacks**
   - SQL injection vulnerabilities
   - Cross-site scripting (XSS) risks
   - Command injection possibilities
   - Path traversal vulnerabilities
   - Input sanitization gaps

2. **Authentication & Authorization**
   - Password security weaknesses
   - JWT token handling issues
   - Session management flaws
   - Access control bypasses
   - Multi-factor authentication gaps
   - Rate limiting deficiencies

3. **Data Protection & Privacy**
   - Sensitive data exposure
   - Inadequate encryption
   - GDPR compliance issues
   - Data retention problems
   - Logging security risks
   - Database security gaps

4. **Cryptographic Security**
   - Weak encryption algorithms
   - Hardcoded secrets and keys
   - Poor key management
   - Predictable random numbers
   - Certificate validation issues
   - Insecure password hashing

5. **Network & API Security**
   - CORS misconfigurations
   - Missing HTTPS enforcement
   - API security vulnerabilities
   - Request size limit bypasses
   - Information disclosure in errors
   - Missing security headers

6. **Dependency & Supply Chain**
   - Vulnerable dependencies
   - Outdated libraries
   - Dependency confusion risks
   - Supply chain attack vectors

## Instructions:
1. Analyze the code changes in the working directory
2. Focus on security vulnerabilities and risks
3. Provide specific findings with exact code locations
4. Explain the security impact and attack vectors
5. Suggest concrete remediation steps

Please perform a thorough security analysis of the code changes.
"""
        return prompt.strip()

    async def analyze(self, context: Dict[str, Any]) -> AgentResult:
        """
        Execute security analysis on the provided code context.
        
        Args:
            context: Analysis context containing changed files, working directory, etc.
            
        Returns:
            AgentResult with security analysis findings
        """
        try:
            logger.info(f"Starting security analysis for agent {self.agent_id}")
            
            # Extract security analysis context
            security_context = await self._extract_security_context(context)
            
            # Get security-focused prompt with file analysis
            prompt = await self._build_security_prompt(security_context)
            
            # Execute Claude Code SDK query for security analysis  
            analysis_result = await self.execute_claude_query(
                prompt=prompt,
                context=context,
                max_turns=8  # Security analysis may need multiple turns for thorough investigation
            )
            
            # Process and structure the security analysis result
            structured_result = await self._process_security_analysis(analysis_result, security_context)
            
            logger.info(f"Security analysis completed for agent {self.agent_id}")
            
            result = AgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                status=AgentExecutionStatus.SUCCESS,
                success=True
            )
            result.set_result_data({"analysis": structured_result})
            result.context_data = {
                "files_analyzed": len(security_context.get("changed_files", [])),
                "security_categories_checked": [
                    "input_validation", "sql_injection", "xss", "auth", 
                    "data_exposure", "crypto", "dependencies"
                ],
                "severity_threshold": self.severity_threshold
            }
            return result
            
        except Exception as e:
            logger.error(f"Security analysis failed for agent {self.agent_id}: {str(e)}")
            result = AgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                status=AgentExecutionStatus.FAILED,
                success=False,
                error_message=str(e)
            )
            result.set_result_data({"error": f"Security analysis failed: {str(e)}"})
            return result
    
    async def _extract_security_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract security-relevant context from the general context."""
        security_context = {
            "changed_files": context.get("changed_files", []),
            "working_path": context.get("working_path", "."),
            "branch_name": context.get("branch_name", ""),
            "ticket_id": context.get("ticket_id", ""),
            "jira_info": context.get("jira_info", {}),
        }
        
        # Filter for security-relevant file types
        relevant_files = []
        security_extensions = {'.py', '.js', '.ts', '.tsx', '.jsx', '.java', '.php', '.cs', '.rb', '.go', '.rs'}
        
        for file_path in security_context["changed_files"]:
            if Path(file_path).suffix.lower() in security_extensions:
                relevant_files.append(file_path)
        
        security_context["security_relevant_files"] = relevant_files
        
        # Check for configuration files that might contain secrets
        config_files = []
        config_patterns = {'.env', 'config', 'settings', 'secrets', 'credentials'}
        
        for file_path in security_context["changed_files"]:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in config_patterns):
                config_files.append(file_path)
        
        security_context["config_files"] = config_files
        
        return security_context
    
    async def _build_security_prompt(self, security_context: Dict[str, Any]) -> str:
        """Build the security analysis prompt based on context."""
        
        # Load base security prompt (German, from existing system)
        base_prompt = """# Security Analysis Review Prompt

Du führst eine **tiefgreifende Security-Analyse** durch mit Fokus auf Vulnerabilities, Authentication und Data Protection.

**⚠️ WICHTIGE ANWEISUNG: Verwende das Read tool um Code zu analysieren und zeige IMMER konkrete Code-Snippets mit exakten Datei:Zeile Referenzen!**

**ANALYSE-VORGEHEN:**
1. **Read verwenden:** Lies alle relevanten Dateien mit dem Read tool
2. **Exakte Locations:** Gib IMMER präzise Datei:Zeile Referenzen an
3. **Code zeigen:** Kopiere den tatsächlichen Code aus den Dateien
4. **Context bieten:** Zeige 2-3 Zeilen vor/nach für Verständnis

## 🎯 REVIEW-FOKUS: SECURITY ANALYSIS

### HAUPTZIEL
Systematische Identifikation von Security-Vulnerabilities, Authentication-Issues und Data Protection-Problemen mit konkreten Fix-Empfehlungen.

## 🔍 SECURITY-KATEGORIEN

### 1. 🔒 INPUT VALIDATION & INJECTION ATTACKS
**Schwerpunkt**: Schutz vor Injection-Angriffen

- **SQL Injection**: Fehlende Parameterisierung, String-Interpolation in Queries
- **XSS (Cross-Site Scripting)**: Unescaped user input in HTML output
- **Command Injection**: Unsicherer System-Call mit User-Input
- **Path Traversal**: Unvalidierte Dateipfade, Directory Traversal
- **LDAP/NoSQL Injection**: Injection in alternative Datenbank-Systeme
- **Input Sanitization**: Fehlende oder unzureichende Input-Validation

### 2. 🔐 AUTHENTICATION & AUTHORIZATION
**Schwerpunkt**: Sichere Benutzer-Authentifizierung und Zugriffskontrolle

- **Password Security**: Schwache Passwort-Requirements, unsichere Speicherung
- **JWT Security**: Unsichere Token-Verarbeitung, fehlende Signatur-Validierung
- **Session Management**: Session-Fixation, fehlende Session-Invalidierung
- **Access Control**: Fehlende Authorization-Checks, Privilege Escalation
- **Multi-Factor Authentication**: Fehlende oder unsichere MFA-Implementierung
- **Rate Limiting**: Fehlender Schutz vor Brute-Force-Attacken

### 3. 📊 DATA PROTECTION & PRIVACY
**Schwerpunkt**: Schutz sensibler Daten

- **Sensitive Data Exposure**: Passwords, API-Keys, PII in Logs oder Responses
- **Data Encryption**: Unverschlüsselte sensitive Daten in Storage/Transit
- **GDPR Compliance**: Fehlende Data Protection Maßnahmen
- **Data Retention**: Unsichere Speicherung persönlicher Daten
- **Logging Security**: Sensitive Daten in Application Logs
- **Database Security**: Unverschlüsselte Datenbank-Verbindungen

### 4. 🔧 CRYPTOGRAPHIC SECURITY
**Schwerpunkt**: Sichere Kryptographie-Implementierung

- **Weak Encryption**: Veraltete Verschlüsselungsalgorithmen (MD5, SHA1, DES)
- **Hardcoded Secrets**: API-Keys, Passwords, Tokens im Source-Code
- **Key Management**: Unsichere Schlüssel-Generierung oder -Speicherung
- **Random Number Generation**: Predictable Random-Number-Generatoren
- **Certificate Validation**: Fehlende oder unzureichende TLS-Validierung
- **Hashing**: Unsichere Password-Hashing ohne Salt

### 5. 🌐 NETWORK & API SECURITY
**Schwerpunkt**: Sichere Netzwerk- und API-Kommunikation

- **CORS Misconfiguration**: Overly permissive CORS-Settings
- **HTTPS Enforcement**: Fehlende TLS-Verschlüsselung
- **API Security**: Fehlende Input-Validation, unsichere Endpoints
- **Request Size Limits**: Fehlende Limits für Request-Größe (DoS-Schutz)
- **Error Information Disclosure**: Stack Traces oder interne Infos in Error Messages
- **Content Security Policy**: Fehlende oder schwache CSP-Header

### 6. 📦 DEPENDENCY & SUPPLY CHAIN SECURITY
**Schwerpunkt**: Sichere Dependencies und Supply Chain

- **Vulnerable Dependencies**: Bekannte CVEs in verwendeten Packages
- **Outdated Libraries**: Veraltete Versionen mit Security-Patches
- **Dependency Confusion**: Unsichere Package-Namen oder -Quellen
- **Supply Chain Attacks**: Kompromittierte Dependencies
- **License Compliance**: Security-relevante License-Issues
- **Dependency Scanning**: Fehlende automatische Vulnerability-Scans

## 📊 DETAILLIERTES OUTPUT FORMAT

### SECURITY DASHBOARD
```markdown
## 🔒 SECURITY ANALYSIS EXECUTIVE DASHBOARD

**Overall Security Risk**: [CRITICAL/HIGH/MEDIUM/LOW]
**Vulnerability Count**: X Critical, Y High, Z Medium, W Low
**OWASP Top 10 Coverage**: [Percentage]
**Compliance Status**: [GDPR/PCI DSS/SOC2 relevant findings]

### Critical Security Issues
- 🚨 Critical Issues: X (Must fix immediately)
- ❌ High Risk: Y (Fix before merge)  
- ⚠️ Medium Risk: Z (Fix in next sprint)
- 💡 Security Improvements: W (Nice to have)

### Vulnerability Categories
- 🔒 Input Validation: X
- 🔐 Authentication: Y  
- 📊 Data Protection: Z
- 🔧 Cryptography: W
- 🌐 Network Security: V
- 📦 Dependencies: U
```

### CRITICAL VULNERABILITIES SECTION
```markdown
## 🚨 CRITICAL SECURITY VULNERABILITIES

### 1. SQL Injection Vulnerability
**File**: `src/services/userService.ts:67`
**Severity**: CRITICAL
**OWASP**: A03:2021 – Injection
**CWE**: CWE-89
**Risk**: Complete database compromise, data breach

**Vulnerable Code**:
```typescript
const query = `SELECT * FROM users WHERE email = '${email}'`;
const result = await db.query(query);
```

**Attack Vector**: 
```javascript
email = "'; DROP TABLE users; --"
```

**Impact**: 
- Complete database access
- Data exfiltration possible
- System compromise

**Fix**:
```typescript
const query = 'SELECT * FROM users WHERE email = ?';
const result = await db.query(query, [email]);
// oder mit ORM:
const user = await User.findOne({ where: { email } });
```

**Verification**:
- Static Analysis: ESLint security rules
- Dynamic Testing: SQLmap, Burp Suite
- Unit Tests: Test with malicious payloads

---

### 2. Hardcoded API Key Exposure
**File**: `src/config/apiConfig.ts:12`
**Severity**: CRITICAL
**OWASP**: A07:2021 – Identification and Authentication Failures
**CWE**: CWE-798
**Risk**: Unauthorized API access, data breach

**Problematic Code**:
```typescript
const API_KEY = "sk-1234567890abcdef-hardcoded-secret";
export const apiConfig = {
  apiKey: API_KEY,
  endpoint: "https://api.example.com"
};
```

**Impact**:
- API quota abuse
- Unauthorized data access
- Financial costs from API overuse

**Fix**:
```typescript
const API_KEY = process.env.API_KEY;
if (!API_KEY) {
  throw new Error('API_KEY environment variable is required');
}
export const apiConfig = {
  apiKey: API_KEY,
  endpoint: process.env.API_ENDPOINT || "https://api.example.com"
};
```

**Additional Security**:
- Use Key Vault/Secret Manager
- Implement API key rotation
- Monitor API usage
```

### HIGH RISK SECTION
```markdown
## ❌ HIGH RISK VULNERABILITIES

### 1. Missing Input Validation
**File**: `src/controllers/userController.ts:45-67`
**Severity**: HIGH
**OWASP**: A03:2021 – Injection
**Risk**: XSS, data corruption, system instability

**Problem**: Direct use of user input without validation
```typescript
app.post('/user/profile', (req, res) => {
  const { name, email, bio } = req.body;
  // Direkter Database-Insert ohne Validation!
  db.insertUser({ name, email, bio });
});
```

**Fix**: Implement comprehensive input validation
```typescript
import Joi from 'joi';

const userSchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  bio: Joi.string().max(500).optional()
});

app.post('/user/profile', async (req, res) => {
  try {
    const validatedData = await userSchema.validateAsync(req.body);
    await db.insertUser(validatedData);
    res.json({ success: true });
  } catch (error) {
    res.status(400).json({ error: 'Invalid input data' });
  }
});
```

---

### 2. Weak Password Requirements
**File**: `src/auth/passwordValidator.ts:15-22`
**Severity**: HIGH
**OWASP**: A07:2021 – Identification and Authentication Failures
**Risk**: Account compromise, brute force attacks

**Current Implementation**:
```typescript
function isValidPassword(password: string): boolean {
  return password.length >= 6; // Zu schwach!
}
```

**Security-compliant Fix**:
```typescript
function isValidPassword(password: string): boolean {
  const minLength = 12;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}\\|<>]/.test(password);
  
  return password.length >= minLength &&
         hasUpperCase &&
         hasLowerCase &&
         hasNumbers &&
         hasSpecialChar;
}
```
```

### MEDIUM RISK & RECOMMENDATIONS
```markdown
## ⚠️ MEDIUM RISK SECURITY ISSUES

### 1. Missing CSRF Protection
**Files**: Multiple controller files
**Impact**: Cross-site request forgery attacks
**Fix**: Implement CSRF tokens for state-changing operations

### 2. Overly Permissive CORS
**File**: `src/config/cors.ts`
**Impact**: Unauthorized cross-origin requests
**Fix**: Restrict CORS to specific domains

### 3. Error Information Disclosure
**Files**: Error handling middleware
**Impact**: Information leakage to attackers
**Fix**: Generic error messages in production

## 💡 SECURITY IMPROVEMENTS & BEST PRACTICES

### Authentication Enhancements
1. **Multi-Factor Authentication**: Implement TOTP/SMS verification
2. **Password Policy**: Enforce strong password requirements
3. **Account Lockout**: Implement brute-force protection
4. **Session Security**: Secure session configuration

### Infrastructure Security
1. **Security Headers**: Implement comprehensive security headers
2. **Rate Limiting**: API endpoint protection
3. **Logging**: Security event logging and monitoring
4. **Dependency Scanning**: Automated vulnerability scanning
```

### COMPLIANCE & GOVERNANCE
```markdown
## 📋 SECURITY COMPLIANCE ASSESSMENT

### GDPR Compliance Status: NEEDS ATTENTION
**Data Protection Issues**:
- Personal data in application logs
- Missing consent mechanisms
- No data retention policies
- Insufficient encryption at rest

### OWASP Top 10 2021 Coverage
- ✅ A01: Broken Access Control - Covered
- ❌ A02: Cryptographic Failures - Issues found
- ❌ A03: Injection - Critical issues found
- ⚠️ A04: Insecure Design - Partially addressed
- ❌ A05: Security Misconfiguration - Issues found
- ⚠️ A06: Vulnerable Components - Scanning needed
- ❌ A07: Authentication Failures - Issues found
- ⚠️ A08: Software Integrity Failures - Needs review
- ❌ A09: Security Logging Failures - Insufficient
- ⚠️ A10: Server-Side Request Forgery - Needs review

### Security Action Plan
**PHASE 1: Critical Fixes (Immediate)**
- [ ] Fix SQL injection vulnerabilities
- [ ] Remove hardcoded secrets
- [ ] Implement input validation
- [ ] Fix authentication issues

**PHASE 2: High Priority (This Sprint)**
- [ ] Implement CSRF protection
- [ ] Fix CORS configuration
- [ ] Add security headers
- [ ] Implement rate limiting

**PHASE 3: Compliance (Next Sprint)**
- [ ] GDPR compliance review
- [ ] Security logging implementation
- [ ] Dependency vulnerability scanning
- [ ] Security testing automation
```

## 🔍 ANALYSE-RICHTLINIEN

### Security Investigation Approach
- **Threat Modeling**: Identify potential attack vectors
- **Code Pattern Analysis**: Look for security anti-patterns
- **Configuration Review**: Check security configurations
- **Dependency Analysis**: Review third-party security

### Risk Assessment Criteria
- **Business Impact**: Data breach, financial loss, reputation damage
- **Technical Impact**: System compromise, service disruption
- **Compliance Impact**: Regulatory violations, legal issues
- **User Impact**: Privacy violations, account compromise

### Remediation Priorities
1. **Critical**: Immediate system compromise risk
2. **High**: Significant security risk, fix before merge
3. **Medium**: Security improvement, fix in next sprint
4. **Low**: Best practice improvement, address when possible

## ⚠️ WICHTIGE SECURITY-ANALYSE-REGELN

1. **Security First**: Alle Critical/High Vulnerabilities müssen behoben werden
2. **Evidence-Based**: Konkrete Code-Stellen und Exploit-Beispiele
3. **Practical Fixes**: Umsetzbare Lösungen mit Code-Beispielen
4. **Compliance-Aware**: GDPR, OWASP, CWE Referenzen
5. **Risk-Prioritized**: Nach tatsächlichem Security-Impact sortieren
6. **Verification Focus**: Testbare Security-Fixes mit Validierung

Führe eine systematische, tiefgreifende Security-Analyse durch mit Fokus auf praktische Vulnerabilities und umsetzbare Sicherheitsverbesserungen."""

        # Add context-specific information
        files_section = ""
        if security_context.get("security_relevant_files"):
            files_section = f"""

## 📁 SECURITY-RELEVANTE DATEIEN FÜR ANALYSE

Analysiere besonders diese sicherheitsrelevanten Dateien:
{chr(10).join(f"- {file}" for file in security_context["security_relevant_files"])}
"""

        config_section = ""
        if security_context.get("config_files"):
            config_section = f"""

## ⚙️ KONFIGURATIONSDATEIEN (SECRETS-PRÜFUNG)

Prüfe diese Konfigurationsdateien auf hardcoded secrets:
{chr(10).join(f"- {file}" for file in security_context["config_files"])}
"""

        context_section = f"""

## 🎯 ANALYSIS CONTEXT

**Branch**: {security_context.get('branch_name', 'unknown')}
**Ticket**: {security_context.get('ticket_id', 'unknown')}
**Working Directory**: {security_context.get('working_path', '.')}
**Files to Analyze**: {len(security_context.get('changed_files', []))} files
"""

        return base_prompt + context_section + files_section + config_section

    async def _process_security_analysis(self, analysis_result: Dict[str, Any], security_context: Dict[str, Any]) -> str:
        """Process and structure the security analysis result."""
        
        # Extract the actual analysis content from the response
        content = ""
        if isinstance(analysis_result, dict):
            # Try to extract content from various possible keys
            content = (analysis_result.get('content') or 
                      analysis_result.get('response') or 
                      analysis_result.get('result') or 
                      str(analysis_result))
        else:
            content = str(analysis_result)
        
        # Add metadata and summary to the result
        metadata_header = f"""# 🔒 SECURITY ANALYSIS REPORT

**Agent**: {self.display_name} ({self.agent_id})
**Analysis Date**: {datetime.now().isoformat()}
**Files Analyzed**: {len(security_context.get('security_relevant_files', []))}
**Configuration Files Checked**: {len(security_context.get('config_files', []))}
**Severity Threshold**: {self.severity_threshold}

---

"""
        
        return metadata_header + content

    def get_agent_info(self) -> Dict[str, Any]:
        """Return agent information for factory registration."""
        return {
            "agent_type": self.agent_type,
            "display_name": self.display_name,
            "description": self.description,
            "capabilities": [
                "SQL Injection Detection",
                "XSS Vulnerability Analysis", 
                "Authentication Security Review",
                "Sensitive Data Exposure Detection",
                "Cryptographic Security Analysis",
                "Dependency Vulnerability Scanning"
            ],
            "supported_file_types": [".py", ".js", ".ts", ".tsx", ".jsx", ".java", ".php", ".cs", ".rb", ".go", ".rs"],
            "estimated_execution_time": "8-15 minutes",
            "parallel_compatible": True
        }