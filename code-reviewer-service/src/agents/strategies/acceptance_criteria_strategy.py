"""
Acceptance Criteria Agent Strategy

Implements the first concrete agent for acceptance criteria validation.
This agent focuses on validating code implementation against defined acceptance criteria.
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional
import json
import re

from ..base_agent import BaseAgent
from ..models import AgentR<PERSON>ult, AgentExecutionStatus
from ...services.claude_service import Claude<PERSON>ervice
from ...config.settings import Settings


logger = logging.getLogger(__name__)


class AcceptanceCriteriaAgent(BaseAgent):
    """
    Agent for validating code implementation against acceptance criteria.
    
    This agent:
    1. Extracts acceptance criteria from Jira tickets or context
    2. Analyzes code implementation for AC compliance
    3. Provides detailed AC-by-AC validation results
    4. Identifies gaps and missing implementations
    """
    
    def __init__(
        self,
        agent_id: str,
        agent_type: str,
        claude_service: ClaudeService,
        settings: Settings,
        timeout_seconds: int = 300
    ):
        """Initialize the Acceptance Criteria Agent."""
        super().__init__(agent_id, agent_type, claude_service, settings, timeout_seconds)
        
        # AC-specific configuration
        self.focus_areas = [
            "requirement_compliance",
            "acceptance_testing",
            "business_logic_validation",
            "user_story_fulfillment"
        ]
        
        # Jira integration patterns
        self.jira_ticket_patterns = [
            r'([A-Z]+-\d+)',  # Standard Jira pattern like "CMS20-1234"
            r'feature/([A-Z]+-\d+)',  # Branch pattern
            r'bugfix/([A-Z]+-\d+)',   # Bugfix branch pattern
        ]
    
    async def analyze(self, context: Dict[str, Any]) -> AgentResult:
        """
        Main analysis method for acceptance criteria validation.
        
        Args:
            context: Analysis context containing code, Jira info, etc.
            
        Returns:
            AgentResult with AC validation findings
        """
        result = AgentResult(
            agent_id=self.agent_id,
            agent_type=self.agent_type,
            context_data=context.copy()
        )
        
        try:
            result.add_progress_update("Starting acceptance criteria analysis")
            
            # Extract Jira ticket information
            jira_info = await self._extract_jira_context(context)
            result.context_data["jira_info"] = jira_info
            
            # Get acceptance criteria from multiple sources
            acceptance_criteria = await self._get_acceptance_criteria(context, jira_info)
            result.add_progress_update(f"Found {len(acceptance_criteria)} acceptance criteria")
            
            # Prepare analysis context
            analysis_context = self._prepare_analysis_context(context, jira_info, acceptance_criteria)
            
            # Generate the prompt
            prompt = self.get_prompt(analysis_context)
            
            # Execute Claude analysis
            result.add_progress_update("Executing Claude Code analysis")
            claude_response = await self.execute_claude_query(
                prompt=prompt,
                context=context,
                max_turns=5
            )
            
            # Process and structure the results
            structured_results = await self._process_claude_response(
                claude_response, acceptance_criteria
            )
            
            # Update result with findings
            # Extract content and store as raw output
            if isinstance(claude_response, dict):
                content = (claude_response.get('content') or 
                          claude_response.get('response') or 
                          claude_response.get('result') or 
                          str(claude_response))
            else:
                content = str(claude_response)
            
            result.raw_output = content
            result.structured_output = structured_results
            result.result_data = {
                "acceptance_criteria_count": len(acceptance_criteria),
                "compliance_rate": structured_results.get("compliance_rate", 0.0),
                "fully_implemented": structured_results.get("fully_implemented", []),
                "partially_implemented": structured_results.get("partially_implemented", []),
                "not_implemented": structured_results.get("not_implemented", []),
                "issues": structured_results.get("issues", [])
            }
            
            # Set quality metrics
            result.set_quality_metrics({
                "compliance_score": structured_results.get("compliance_rate", 0.0),
                "critical_gaps": len(structured_results.get("not_implemented", [])),
                "partial_implementations": len(structured_results.get("partially_implemented", [])),
                "analysis_confidence": structured_results.get("confidence_score", 0.8)
            })
            
            result.add_progress_update("Acceptance criteria analysis completed")
            self.logger.info(f"AC analysis completed with {structured_results.get('compliance_rate', 0)}% compliance")
            
            return result
            
        except Exception as e:
            self.logger.error(f"AC analysis failed: {str(e)}", exc_info=True)
            result.mark_failed(
                error_message=f"Acceptance criteria analysis failed: {str(e)}",
                error_details={"exception_type": type(e).__name__}
            )
            return result
    
    def get_prompt(self, context: Dict[str, Any]) -> str:
        """
        Get the AC-specific prompt template with context injection.
        
        Args:
            context: Analysis context with AC and code information
            
        Returns:
            Formatted prompt string for Claude
        """
        # Base German AC prompt (from the existing system)
        base_prompt = """Du führst eine **fokussierte Acceptance Criteria Review** durch mit Schwerpunkt auf Business Requirements und Functional Compliance.

**⚠️ WICHTIGE ANWEISUNG: Verwende das Read tool um Code zu analysieren und zeige IMMER konkrete Code-Snippets mit exakten Datei:Zeile Referenzen!**

**ANALYSE-VORGEHEN:**
1. **Read verwenden:** Lies alle relevanten Dateien mit dem Read tool
2. **Exakte Locations:** Gib IMMER präzise Datei:Zeile Referenzen an
3. **Code zeigen:** Kopiere den tatsächlichen Code aus den Dateien  
4. **Context bieten:** Zeige 2-3 Zeilen vor/nach für Verständnis

## 🎯 REVIEW-FOKUS: ACCEPTANCE CRITERIA COMPLIANCE

### HAUPTZIEL
Detaillierte Analyse der Implementierung gegen alle definierten Acceptance Criteria mit Business-Kontext und Gap-Analyse.

## 📋 ANALYSE-STRUKTUR

### 1. TICKET CONTEXT EXTRACTION
**Verstehe das Business Problem**:
- Jira Ticket Details analysieren
- User Stories und Business Value erfassen  
- Stakeholder Impact bewerten
- Technical Requirements ableiten

### 2. AC MAPPING & VALIDATION
**Systematische AC-Überprüfung**:
- Jedes AC einzeln gegen Code validieren
- Implementation Paths nachvollziehen
- Test Coverage für AC überprüfen
- Edge Cases für jedes AC bewerten

### 3. BUSINESS LOGIC VALIDATION
**Fachliche Korrektheit prüfen**:
- Workflow Compliance
- Data Integrity Checks
- User Experience Validation
- Integration Points Review

### 4. GAP ANALYSIS & RECOMMENDATIONS
**Lücken identifizieren und Lösungen anbieten**:
- Nicht implementierte AC
- Partielle Implementierungen
- Missing Test Cases
- Documentation Gaps"""
        
        # Add context-specific information
        context_info = self._format_context_for_prompt(context)
        
        return f"{base_prompt}\n\n{context_info}"
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent type."""
        return """Du bist ein Experte für Acceptance Criteria Validation mit tiefgreifendem Wissen über Business Requirements und Software-Compliance.

Deine Aufgabe ist es, fokussierte AC-Reviews durchzuführen, die Folgendes umfassen:
1. Vollständige AC-Compliance Analyse
2. Business Logic Validation  
3. Gap-Identifikation und Lösungsvorschläge
4. Test Coverage für Requirements

Sei gründlich, spezifisch und gib umsetzbare Empfehlungen mit exakten Code-Stellen und Fixes."""
    
    async def _extract_jira_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract Jira ticket information from various sources.
        
        Args:
            context: Analysis context
            
        Returns:
            Jira information dictionary
        """
        jira_info = {}
        
        # Try to get Jira ticket ID from branch name
        git_branch = context.get("git_branch", "")
        if git_branch:
            ticket_id = self._extract_ticket_id_from_branch(git_branch)
            if ticket_id:
                jira_info["ticket_id"] = ticket_id
        
        # Try to get from explicit context
        if "jira_ticket_id" in context:
            jira_info["ticket_id"] = context["jira_ticket_id"]
        
        # Try to get ticket data if available
        if "jira_ticket_data" in context:
            jira_info.update(context["jira_ticket_data"])
        
        # Fallback: look for ticket.md file in working directory
        working_path = Path(context.get("working_path", "."))
        ticket_file = working_path / "ticket.md"
        
        if ticket_file.exists():
            try:
                ticket_content = ticket_file.read_text(encoding="utf-8")
                jira_info["ticket_content"] = ticket_content
                jira_info["source"] = "ticket.md"
            except Exception as e:
                self.logger.warning(f"Could not read ticket.md: {str(e)}")
        
        return jira_info
    
    def _extract_ticket_id_from_branch(self, branch_name: str) -> Optional[str]:
        """Extract Jira ticket ID from branch name."""
        for pattern in self.jira_ticket_patterns:
            match = re.search(pattern, branch_name)
            if match:
                return match.group(1) if len(match.groups()) > 0 else match.group(0)
        return None
    
    async def _get_acceptance_criteria(
        self,
        context: Dict[str, Any],
        jira_info: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Get acceptance criteria from various sources.
        
        Args:
            context: Analysis context
            jira_info: Jira ticket information
            
        Returns:
            List of acceptance criteria
        """
        acceptance_criteria = []
        
        # Source 1: Direct from context
        if "acceptance_criteria" in context:
            criteria = context["acceptance_criteria"]
            if isinstance(criteria, list):
                acceptance_criteria.extend(criteria)
            elif isinstance(criteria, str):
                # Parse string format
                parsed_criteria = self._parse_criteria_string(criteria)
                acceptance_criteria.extend(parsed_criteria)
        
        # Source 2: From Jira ticket data
        if "acceptance_criteria" in jira_info:
            jira_criteria = jira_info["acceptance_criteria"]
            if isinstance(jira_criteria, list):
                acceptance_criteria.extend(jira_criteria)
        
        # Source 3: Parse from ticket content
        if "ticket_content" in jira_info:
            parsed_criteria = self._parse_criteria_from_ticket_content(
                jira_info["ticket_content"]
            )
            acceptance_criteria.extend(parsed_criteria)
        
        # Ensure each AC has required fields
        standardized_criteria = []
        for i, ac in enumerate(acceptance_criteria):
            if isinstance(ac, str):
                standardized_criteria.append({
                    "id": f"AC{i+1}",
                    "description": ac,
                    "priority": "medium",
                    "category": "functional"
                })
            elif isinstance(ac, dict):
                # Ensure required fields
                ac.setdefault("id", f"AC{i+1}")
                ac.setdefault("priority", "medium")
                ac.setdefault("category", "functional")
                standardized_criteria.append(ac)
        
        return standardized_criteria
    
    def _parse_criteria_string(self, criteria_string: str) -> List[Dict[str, Any]]:
        """Parse acceptance criteria from a string format."""
        criteria = []
        
        # Split by common delimiters
        lines = criteria_string.split('\n')
        for i, line in enumerate(lines):
            line = line.strip()
            if line and not line.startswith('#'):
                # Remove bullet points, numbers, etc.
                clean_line = re.sub(r'^[-*•\d\.]+\s*', '', line)
                if clean_line:
                    criteria.append({
                        "id": f"AC{i+1}",
                        "description": clean_line,
                        "priority": "medium",
                        "category": "functional"
                    })
        
        return criteria
    
    def _parse_criteria_from_ticket_content(self, content: str) -> List[Dict[str, Any]]:
        """Parse acceptance criteria from ticket content."""
        criteria = []
        
        # Look for common AC sections
        ac_patterns = [
            r'#{1,3}\s*Acceptance Criteria.*?\n(.*?)(?=\n#{1,3}|\n\n|\Z)',
            r'#{1,3}\s*AC.*?\n(.*?)(?=\n#{1,3}|\n\n|\Z)',
            r'#{1,3}\s*Requirements.*?\n(.*?)(?=\n#{1,3}|\n\n|\Z)',
        ]
        
        for pattern in ac_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                parsed = self._parse_criteria_string(match)
                criteria.extend(parsed)
        
        return criteria
    
    def _prepare_analysis_context(
        self,
        context: Dict[str, Any],
        jira_info: Dict[str, Any],
        acceptance_criteria: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Prepare the analysis context for the prompt."""
        return {
            **context,
            "jira_info": jira_info,
            "acceptance_criteria": acceptance_criteria,
            "agent_type": self.agent_type,
            "focus_areas": self.focus_areas
        }
    
    def _format_context_for_prompt(self, context: Dict[str, Any]) -> str:
        """Format context information for the prompt."""
        sections = []
        
        # Jira Information
        jira_info = context.get("jira_info", {})
        if jira_info:
            sections.append("## 📋 TICKET KONTEXT")
            if "ticket_id" in jira_info:
                sections.append(f"**Ticket ID**: {jira_info['ticket_id']}")
            if "summary" in jira_info:
                sections.append(f"**Summary**: {jira_info['summary']}")
            if "description" in jira_info:
                sections.append(f"**Description**: {jira_info['description']}")
        
        # Acceptance Criteria
        acceptance_criteria = context.get("acceptance_criteria", [])
        if acceptance_criteria:
            sections.append("## ✅ ACCEPTANCE CRITERIA ZU VALIDIEREN")
            for i, ac in enumerate(acceptance_criteria, 1):
                if isinstance(ac, dict):
                    description = ac.get("description", "No description")
                    priority = ac.get("priority", "medium")
                    sections.append(f"**AC{i}** ({priority}): {description}")
                else:
                    sections.append(f"**AC{i}**: {ac}")
        
        # Working Directory
        working_path = context.get("working_path", ".")
        sections.append(f"## 📁 ARBEITSVERZEICHNIS\n**Path**: {working_path}")
        
        # Analysis Focus
        sections.append("## 🎯 ANALYSE-FOKUS")
        sections.append("- Vollständige AC-Compliance Überprüfung")
        sections.append("- Code-Implementation gegen Requirements")
        sections.append("- Test Coverage für jedes AC")
        sections.append("- Gap-Analyse und Empfehlungen")
        
        return "\n\n".join(sections)
    
    async def _process_claude_response(
        self,
        claude_response: Dict[str, Any],
        acceptance_criteria: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Process Claude's response into structured results.
        
        Args:
            claude_response: Raw response from Claude
            acceptance_criteria: Original acceptance criteria
            
        Returns:
            Structured analysis results
        """
        # Initialize result structure
        results = {
            "compliance_rate": 0.0,
            "fully_implemented": [],
            "partially_implemented": [],
            "not_implemented": [],
            "issues": [],
            "confidence_score": 0.8
        }
        
        try:
            # Parse the response for AC status indicators
            for ac in acceptance_criteria:
                ac_id = ac.get("id", "")
                ac_description = ac.get("description", "")
                
                # Extract content for processing
                response_text = ""
                if isinstance(claude_response, dict):
                    response_text = (claude_response.get('content') or 
                                   claude_response.get('response') or 
                                   claude_response.get('result') or 
                                   str(claude_response))
                else:
                    response_text = str(claude_response)
                
                # Look for status indicators in the response
                status = self._extract_ac_status_from_response(
                    response_text, ac_id, ac_description
                )
                
                ac_result = {
                    "id": ac_id,
                    "description": ac_description,
                    "status": status,
                    "details": self._extract_ac_details_from_response(
                        response_text, ac_id
                    )
                }
                
                # Categorize based on status
                if status == "fully_implemented":
                    results["fully_implemented"].append(ac_result)
                elif status == "partially_implemented":
                    results["partially_implemented"].append(ac_result)
                else:
                    results["not_implemented"].append(ac_result)
            
            # Calculate compliance rate
            total_criteria = len(acceptance_criteria)
            if total_criteria > 0:
                fully_count = len(results["fully_implemented"])
                partially_count = len(results["partially_implemented"])
                # Partial implementations count as 0.5
                compliance_score = (fully_count + partially_count * 0.5) / total_criteria
                results["compliance_rate"] = round(compliance_score * 100, 1)
            
            # Extract issues from response
            # Extract content for issues processing
            response_text = ""
            if isinstance(claude_response, dict):
                response_text = (claude_response.get('content') or 
                               claude_response.get('response') or 
                               claude_response.get('result') or 
                               str(claude_response))
            else:
                response_text = str(claude_response)
            
            results["issues"] = self._extract_issues_from_response(response_text)
            
        except Exception as e:
            self.logger.error(f"Failed to process Claude response: {str(e)}")
            # Return basic structure with error info
            results["processing_error"] = str(e)
        
        return results
    
    def _extract_ac_status_from_response(
        self,
        response: str,
        ac_id: str,
        ac_description: str
    ) -> str:
        """Extract AC implementation status from Claude's response."""
        # Look for status indicators
        patterns = [
            (r'✅.*?' + re.escape(ac_id), "fully_implemented"),
            (r'⚠️.*?' + re.escape(ac_id), "partially_implemented"),
            (r'❌.*?' + re.escape(ac_id), "not_implemented"),
            (r'VOLLSTÄNDIG ERFÜLLT.*?' + re.escape(ac_id), "fully_implemented"),
            (r'TEILWEISE ERFÜLLT.*?' + re.escape(ac_id), "partially_implemented"),
            (r'NICHT ERFÜLLT.*?' + re.escape(ac_id), "not_implemented"),
        ]
        
        for pattern, status in patterns:
            if re.search(pattern, response, re.IGNORECASE | re.DOTALL):
                return status
        
        # Default to not implemented if no clear status found
        return "not_implemented"
    
    def _extract_ac_details_from_response(self, response: str, ac_id: str) -> str:
        """Extract detailed analysis for a specific AC."""
        # Try to find the section about this AC
        pattern = rf'{re.escape(ac_id)}:.*?(?=\n### |$)'
        match = re.search(pattern, response, re.DOTALL | re.IGNORECASE)
        
        if match:
            return match.group(0).strip()
        
        return "No detailed analysis available"
    
    def _extract_issues_from_response(self, response: str) -> List[Dict[str, Any]]:
        """Extract issues/problems from Claude's response."""
        issues = []
        
        # Look for common issue patterns
        issue_patterns = [
            r'🔴.*?kritisch.*?:(.*?)(?=\n|$)',
            r'🟠.*?wichtig.*?:(.*?)(?=\n|$)',
            r'⚠️.*?problem.*?:(.*?)(?=\n|$)',
            r'❌.*?fehlt.*?:(.*?)(?=\n|$)',
        ]
        
        for pattern in issue_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE | re.DOTALL)
            for match in matches:
                issues.append({
                    "type": "gap_analysis",
                    "severity": "medium",
                    "description": match.strip(),
                    "category": "acceptance_criteria"
                })
        
        return issues[:10]  # Limit to 10 issues to avoid overwhelming results
    
    def configure(self, config: Dict[str, Any]) -> None:
        """Configure the agent with additional settings."""
        if "focus_areas" in config:
            self.focus_areas = config["focus_areas"]
        
        if "jira_patterns" in config:
            self.jira_ticket_patterns = config["jira_patterns"]
        
        self.logger.info(f"Configured AC agent with focus areas: {self.focus_areas}")