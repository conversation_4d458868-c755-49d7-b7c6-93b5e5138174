"""
Code Quality Agent Strategy
Implements the CodeQualityAgent for Code Smells, Best Practices and Performance Issues.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from ..base_agent import BaseAgent
from ..models.agent_result import AgentResult, AgentExecutionStatus

logger = logging.getLogger(__name__)


class CodeQualityAgent(BaseAgent):
    """
    Code Quality Agent für Code Smells, Best Practices und Performance Issues.
    
    Responsibilities:
    - Code Smells Detection  
    - Best Practices Validation
    - Performance Issues Identification
    - Maintainability Metrics Analysis
    - SOLID Principles Compliance
    - Design Pattern Usage Assessment
    """
    
    def __init__(self, agent_id: str, agent_type: str, claude_service, settings, timeout_seconds: int = 300, config: Optional[Dict[str, Any]] = None):
        """Initialize Code Quality Agent."""
        super().__init__(agent_id, agent_type, claude_service, settings, timeout_seconds)
        self.agent_type = "code_quality"
        self.display_name = "Code Quality Agent"
        self.description = "Analyzes code quality, detects code smells, validates best practices and identifies maintainability issues"
        
        # Code quality specific configuration
        self.quality_config = config.get("code_quality", {}) if config else {}
        self.complexity_threshold = self.quality_config.get("complexity_threshold", 10)
        self.duplication_threshold = self.quality_config.get("duplication_threshold", 5)
        self.method_length_threshold = self.quality_config.get("method_length", 50)
        self.check_solid_principles = self.quality_config.get("solid_principles", True)
        self.check_design_patterns = self.quality_config.get("design_patterns", True)
        
    def get_system_prompt(self) -> str:
        """Get the system prompt for code quality analysis."""
        return """You are a specialized Code Quality Agent for code review analysis.

Your primary responsibilities:
- Detect code smells and anti-patterns
- Validate SOLID principles compliance
- Analyze maintainability and complexity metrics
- Review best practices implementation
- Identify design pattern misuse or absence
- Assess technical debt and refactoring opportunities

Focus on providing specific, actionable code quality findings with exact line numbers and clear refactoring suggestions."""

    def get_prompt(self, context: Dict[str, Any]) -> str:
        """Generate dynamic prompt for code quality analysis."""
        branch_name = context.get("branch_name", "unknown")
        working_directory = context.get("working_directory", ".")
        
        prompt = f"""
# Code Quality Analysis Request

**Branch:** {branch_name}
**Working Directory:** {working_directory}

## Analysis Focus Areas:

1. **Code Smells Detection**
   - Long methods and large classes
   - Duplicate code and DRY violations
   - Feature envy and data clumps
   - Dead code and unused variables
   - Complex conditional logic

2. **SOLID Principles Compliance**
   - Single Responsibility Principle
   - Open/Closed Principle
   - Liskov Substitution Principle
   - Interface Segregation Principle
   - Dependency Inversion Principle

3. **Maintainability Metrics**
   - Cyclomatic complexity analysis
   - Method and class size evaluation
   - Nesting depth assessment
   - Parameter count validation

4. **Best Practices Validation**
   - Naming conventions consistency
   - Error handling patterns
   - Resource management
   - Security best practices

5. **Design Patterns & Architecture**
   - Appropriate pattern usage
   - Pattern implementation correctness
   - Architectural layer violations
   - Separation of concerns

## Instructions:
1. Analyze the code changes in the working directory
2. Focus on code quality and maintainability issues
3. Provide specific findings with exact line numbers
4. Suggest concrete refactoring solutions
5. Prioritize issues by severity and impact

Please perform a thorough code quality analysis of the changes.
"""
        return prompt.strip()

    async def analyze(self, context: Dict[str, Any]) -> AgentResult:
        """
        Execute code quality analysis on the provided code context.
        
        Args:
            context: Analysis context containing changed files, working directory, etc.
            
        Returns:
            AgentResult with code quality findings
        """
        try:
            logger.info(f"Starting code quality analysis for agent {self.agent_id}")
            
            # Extract code quality context
            quality_context = await self._extract_code_quality_context(context)
            
            # Get code quality prompt with comprehensive analysis
            prompt = await self._build_code_quality_prompt(quality_context)
            
            # Execute Claude Code SDK query for code quality analysis
            analysis_result = await self.execute_claude_query(
                prompt=prompt,
                context=context,
                max_turns=10  # Code quality analysis needs thorough investigation
            )
            
            # Process and structure the code quality result
            structured_result = await self._process_code_quality_analysis(analysis_result, quality_context)
            
            logger.info(f"Code quality analysis completed for agent {self.agent_id}")
            
            result = AgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                status=AgentExecutionStatus.SUCCESS,
                success=True
            )
            result.set_result_data({"analysis": structured_result})
            result.context_data = {
                "files_analyzed": len(quality_context.get("changed_files", [])),
                "quality_categories_checked": [
                    "code_smells", "best_practices", "maintainability", "solid_principles",
                    "design_patterns", "performance", "complexity", "duplication"
                ],
                "complexity_threshold": self.complexity_threshold,
                "duplication_threshold": self.duplication_threshold,
                "method_length_threshold": self.method_length_threshold
            }
            return result
            
        except Exception as e:
            logger.error(f"Code quality analysis failed for agent {self.agent_id}: {str(e)}")
            result = AgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                status=AgentExecutionStatus.FAILED,
                success=False,
                error_message=str(e)
            )
            result.set_result_data({"error": f"Code quality analysis failed: {str(e)}"})
            return result
    
    async def _extract_code_quality_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract code quality relevant context from the general context."""
        quality_context = {
            "changed_files": context.get("changed_files", []),
            "working_path": context.get("working_path", "."),
            "branch_name": context.get("branch_name", ""),
            "ticket_id": context.get("ticket_id", ""),
            "jira_info": context.get("jira_info", {}),
        }
        
        # Filter for code quality relevant file types
        relevant_files = []
        quality_extensions = {'.py', '.js', '.ts', '.tsx', '.jsx', '.java', '.cpp', '.c', '.cs', '.rb', '.go', '.rs', '.php', '.scala', '.kt'}
        
        for file_path in quality_context["changed_files"]:
            if Path(file_path).suffix.lower() in quality_extensions:
                relevant_files.append(file_path)
        
        quality_context["quality_files"] = relevant_files
        
        # Identify class/service files (likely to have design pattern issues)
        class_files = []
        class_patterns = {
            'class', 'service', 'controller', 'manager', 'handler', 'processor',
            'factory', 'builder', 'adapter', 'facade', 'strategy', 'observer'
        }
        
        for file_path in relevant_files:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in class_patterns):
                class_files.append(file_path)
        
        quality_context["class_files"] = class_files
        
        # Identify utility/helper files (potential for duplication)
        utility_files = []
        utility_patterns = {'util', 'helper', 'common', 'shared', 'lib', 'tool'}
        
        for file_path in relevant_files:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in utility_patterns):
                utility_files.append(file_path)
        
        quality_context["utility_files"] = utility_files
        
        # Identify configuration files
        config_files = []
        config_patterns = {'config', 'setting', 'constant', 'env'}
        
        for file_path in quality_context["changed_files"]:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in config_patterns):
                config_files.append(file_path)
        
        quality_context["config_files"] = config_files
        
        return quality_context
    
    async def _build_code_quality_prompt(self, quality_context: Dict[str, Any]) -> str:
        """Build the code quality prompt based on context."""
        
        # Code quality analysis prompt (German)
        base_prompt = """# Code Quality Analysis Review Prompt

Du führst eine **tiefgreifende Code Quality Analyse** durch mit Fokus auf Code Smells, Best Practices, Maintainability und SOLID Principles.

**⚠️ WICHTIGE ANWEISUNG: Verwende das Read tool um Code zu analysieren und zeige IMMER konkrete Code-Snippets mit exakten Datei:Zeile Referenzen!**

**ANALYSE-VORGEHEN:**
1. **Read verwenden:** Lies alle relevanten Dateien mit dem Read tool
2. **Exakte Locations:** Gib IMMER präzise Datei:Zeile Referenzen an
3. **Code zeigen:** Kopiere den tatsächlichen Code aus den Dateien
4. **Refactoring zeigen:** Zeige konkrete Verbesserungsvorschläge mit Code

## 🎯 REVIEW-FOKUS: CODE QUALITY & MAINTAINABILITY

### HAUPTZIEL
Systematische Identifikation von Code Smells, Maintainability-Problemen, Design-Fehlern und Best Practice Violations mit konkreten Refactoring-Empfehlungen.

## 🔍 CODE QUALITY KATEGORIEN

### 1. 🦨 CODE SMELLS DETECTION
**Schwerpunkt**: Klassische Code Smells nach Martin Fowler

- **Long Method**: Methoden über 50 Zeilen, komplexe Funktionen
- **Large Class**: Klassen mit zu vielen Verantwortlichkeiten
- **Duplicate Code**: Code-Duplikation, Copy-Paste Programming
- **Feature Envy**: Methoden nutzen mehr fremde als eigene Daten
- **Data Clumps**: Wiederholende Parameter-Gruppen
- **Primitive Obsession**: Primitive Datentypen statt Domain Objects
- **Switch Statements**: Lange switch/if-else Ketten statt Polymorphism
- **Speculative Generality**: Über-Engineering, unused abstractions
- **Dead Code**: Unused code, unreachable code paths
- **Comments**: Excessive commenting, misleading comments

### 2. 🏗️ SOLID PRINCIPLES COMPLIANCE
**Schwerpunkt**: SOLID Principles Validation

- **Single Responsibility**: Eine Klasse = eine Verantwortlichkeit
- **Open/Closed**: Offen für Erweiterung, geschlossen für Änderung
- **Liskov Substitution**: Subtypen müssen Parent-Type ersetzen können
- **Interface Segregation**: Clients nicht von ungenutzten Interfaces abhängig
- **Dependency Inversion**: High-level nicht von low-level abhängig
- **God Objects**: Klassen mit zu vielen Verantwortlichkeiten
- **Tight Coupling**: Zu starke Abhängigkeiten zwischen Modulen
- **Low Cohesion**: Schwache Zusammengehörigkeit in Modulen

### 3. 🎨 DESIGN PATTERNS & ARCHITECTURE
**Schwerpunkt**: Design Pattern Usage und Architecture

- **Pattern Misuse**: Falsche Pattern-Anwendung, Over-Engineering
- **Pattern Absence**: Fehlende Patterns wo sinnvoll
- **Architectural Violations**: Layer-Violations, Dependency Rules
- **Separation of Concerns**: Business Logic, Presentation, Data Access
- **DRY Violations**: Don't Repeat Yourself Violations
- **YAGNI Violations**: You Aren't Gonna Need It - Over-Engineering
- **KISS Violations**: Keep It Simple, Stupid - Unnötige Komplexität
- **Abstraction Levels**: Inkonsistente Abstraction-Levels

### 4. 📊 MAINTAINABILITY METRICS
**Schwerpunkt**: Messbare Code Quality Metrics

- **Cyclomatic Complexity**: Komplexität von Control Flow (> 10 problematisch)
- **Cognitive Complexity**: Schwierigkeit des Code-Verständnisses
- **Nesting Depth**: Zu tiefe Verschachtelung (> 4 problematisch)
- **Method Length**: Methoden-Länge (> 50 Zeilen problematisch)
- **Class Size**: Klassen-Größe (> 500 Zeilen problematisch)
- **Parameter Count**: Zu viele Parameter (> 5 problematisch)
- **Return Points**: Mehrere Return-Statements pro Methode
- **Technical Debt**: Accumulation of shortcuts and quick fixes

### 5. 🚀 PERFORMANCE & EFFICIENCY
**Schwerpunkt**: Performance-relevante Code Quality Issues

- **Inefficient Algorithms**: O(n²) statt O(n), Wrong data structures
- **Premature Optimization**: Komplexer Code ohne Performance-Benefit
- **Resource Leaks**: Memory leaks, Connection leaks, File handle leaks
- **Unnecessary Computations**: Redundante Berechnungen, Caching opportunities
- **String Concatenation**: Ineffiziente String-Operations
- **Database Access**: N+1 queries, Missing indexes, Lazy loading issues
- **Collection Misuse**: Wrong collection types, Inefficient operations
- **Boxing/Unboxing**: Unnecessary object creation, Auto-boxing issues

### 6. 📝 NAMING & DOCUMENTATION
**Schwerpunkt**: Code Readability und Self-Documentation

- **Poor Naming**: Unclear variable/function names, Abbreviations
- **Inconsistent Naming**: Verschiedene Naming-Conventions
- **Magic Numbers**: Hard-coded numbers ohne Konstanten
- **Magic Strings**: Hard-coded strings ohne Konstanten
- **Misleading Names**: Namen die nicht dem Verhalten entsprechen
- **Hungarian Notation**: Veraltete Naming-Conventions
- **Documentation Debt**: Missing documentation, Outdated comments
- **Self-Documenting Code**: Code should be readable without comments

## 📊 DETAILLIERTES OUTPUT FORMAT

### CODE QUALITY DASHBOARD
```markdown
## 🎨 CODE QUALITY EXECUTIVE DASHBOARD

**Overall Quality Score**: X/10
**Maintainability Index**: [EXCELLENT/GOOD/FAIR/POOR]
**Technical Debt**: [LOW/MEDIUM/HIGH/CRITICAL]
**SOLID Compliance**: X% (Y/5 principles followed)

### Quality Issue Summary
- 🚨 Critical Issues: X (Must refactor before merge)
- ❌ Major Issues: Y (Should fix before merge)  
- ⚠️ Minor Issues: Z (Fix in next sprint)
- 💡 Improvements: W (Nice to have)

### Quality Categories
- 🦨 Code Smells: X
- 🏗️ SOLID Violations: Y
- 🎨 Design Issues: Z
- 📊 Complexity Issues: W
- 🚀 Performance Issues: V
- 📝 Naming Issues: U
```

### CRITICAL QUALITY ISSUES SECTION
```markdown
## 🚨 CRITICAL CODE QUALITY ISSUES

### 1. God Object Anti-Pattern
**File**: `src/services/OrderProcessor.ts:1-456`
**Severity**: CRITICAL
**Code Smell**: Large Class + Feature Envy
**SOLID Violation**: Single Responsibility Principle
**Technical Debt**: HIGH

**Problematic Code Structure**:
```typescript
class OrderProcessor {
  // 456 lines of code - WAY too large!
  
  // Payment processing (should be separate service)
  processPayment(order: Order) { ... }
  validateCreditCard(card: CreditCard) { ... }
  chargeCreditCard(amount: number) { ... }
  
  // Inventory management (should be separate service)  
  checkInventory(items: OrderItem[]) { ... }
  reserveItems(items: OrderItem[]) { ... }
  updateStock(items: OrderItem[]) { ... }
  
  // Email notifications (should be separate service)
  sendOrderConfirmation(order: Order) { ... }
  sendShippingNotification(order: Order) { ... }
  sendInvoice(order: Order) { ... }
  
  // Shipping calculation (should be separate service)
  calculateShippingCost(order: Order) { ... }
  selectShippingProvider(order: Order) { ... }
  
  // Tax calculation (should be separate service)
  calculateTax(order: Order) { ... }
  applyTaxRules(order: Order) { ... }
  
  // ... 30+ more methods doing different things
}
```

**Problems**:
1. **Single Responsibility Violation**: 6+ different responsibilities
2. **Maintainability**: 456 lines impossible to maintain
3. **Testing**: Difficult to unit test individual components
4. **Coupling**: Changes in one area affect entire class
5. **Code Reuse**: Can't reuse individual components

**Refactored Solution**: Separate Services
```typescript
// Single responsibility services
class PaymentService {
  processPayment(order: Order): Promise<PaymentResult> { ... }
  validateCreditCard(card: CreditCard): boolean { ... }
  chargeCreditCard(amount: number): Promise<ChargeResult> { ... }
}

class InventoryService {
  checkInventory(items: OrderItem[]): Promise<InventoryStatus> { ... }
  reserveItems(items: OrderItem[]): Promise<ReservationResult> { ... }
  updateStock(items: OrderItem[]): Promise<void> { ... }
}

class NotificationService {
  sendOrderConfirmation(order: Order): Promise<void> { ... }
  sendShippingNotification(order: Order): Promise<void> { ... }
  sendInvoice(order: Order): Promise<void> { ... }
}

class ShippingService {
  calculateShippingCost(order: Order): Promise<number> { ... }
  selectShippingProvider(order: Order): ShippingProvider { ... }
}

class TaxService {
  calculateTax(order: Order): Promise<number> { ... }
  applyTaxRules(order: Order): TaxCalculation { ... }
}

// Orchestrator with dependency injection
class OrderProcessor {
  constructor(
    private paymentService: PaymentService,
    private inventoryService: InventoryService,
    private notificationService: NotificationService,
    private shippingService: ShippingService,
    private taxService: TaxService
  ) {}
  
  async processOrder(order: Order): Promise<OrderResult> {
    // Orchestrate the services
    const inventoryStatus = await this.inventoryService.checkInventory(order.items);
    if (!inventoryStatus.available) {
      throw new Error('Items not available');
    }
    
    await this.inventoryService.reserveItems(order.items);
    
    const tax = await this.taxService.calculateTax(order);
    const shipping = await this.shippingService.calculateShippingCost(order);
    
    const finalOrder = { ...order, tax, shipping };
    const paymentResult = await this.paymentService.processPayment(finalOrder);
    
    await this.notificationService.sendOrderConfirmation(order);
    
    return { order: finalOrder, payment: paymentResult };
  }
}
```

**Benefits**:
- ✅ Single Responsibility: Each service has one job
- ✅ Testability: Easy to unit test individual services
- ✅ Reusability: Services can be reused in other contexts
- ✅ Maintainability: Changes isolated to specific services
- ✅ Dependency Injection: Loose coupling between components

---

### 2. Duplicate Code Violation (DRY)
**Files**: `src/utils/validators.ts:45-89` & `src/services/userService.ts:123-167`
**Severity**: CRITICAL
**Code Smell**: Duplicate Code
**Technical Debt**: MEDIUM

**Duplicated Validation Logic**:
```typescript
// src/utils/validators.ts:45-89
function validateEmail(email: string): boolean {
  if (!email || typeof email !== 'string') return false;
  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
  if (!emailRegex.test(email)) return false;
  if (email.length > 254) return false;
  const parts = email.split('@');
  if (parts[0].length > 64) return false;
  return true;
}

function validatePhone(phone: string): boolean {
  if (!phone || typeof phone !== 'string') return false;
  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;
  if (!phoneRegex.test(phone)) return false;
  const digitsOnly = phone.replace(/\\D/g, '');
  if (digitsOnly.length < 10 || digitsOnly.length > 15) return false;
  return true;
}

// src/services/userService.ts:123-167 - EXACT SAME CODE!
function validateEmail(email: string): boolean {
  if (!email || typeof email !== 'string') return false;
  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
  if (!emailRegex.test(email)) return false;
  if (email.length > 254) return false;
  const parts = email.split('@');
  if (parts[0].length > 64) return false;
  return true;
}

function validatePhone(phone: string): boolean {
  if (!phone || typeof phone !== 'string') return false;
  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;
  if (!phoneRegex.test(phone)) return false;
  const digitsOnly = phone.replace(/\\D/g, '');
  if (digitsOnly.length < 10 || digitsOnly.length > 15) return false;
  return true;
}
```

**Problems**:
1. **Maintenance Nightmare**: Changes need to be made in multiple places
2. **Bug Propagation**: Bugs duplicated across codebase
3. **Inconsistency Risk**: Versions may diverge over time
4. **Code Bloat**: Unnecessary code duplication

**Refactored Solution**: Centralized Validation
```typescript
// src/utils/validators.ts - Single source of truth
export class ValidationUtils {
  static validateEmail(email: string): ValidationResult {
    if (!email || typeof email !== 'string') {
      return { valid: false, error: 'Email is required and must be string' };
    }
    
    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
    if (!emailRegex.test(email)) {
      return { valid: false, error: 'Invalid email format' };
    }
    
    if (email.length > 254) {
      return { valid: false, error: 'Email too long (max 254 chars)' };
    }
    
    const parts = email.split('@');
    if (parts[0].length > 64) {
      return { valid: false, error: 'Email local part too long (max 64 chars)' };
    }
    
    return { valid: true };
  }
  
  static validatePhone(phone: string): ValidationResult {
    if (!phone || typeof phone !== 'string') {
      return { valid: false, error: 'Phone is required and must be string' };
    }
    
    const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;
    if (!phoneRegex.test(phoneRegex)) {
      return { valid: false, error: 'Invalid phone format' };
    }
    
    const digitsOnly = phone.replace(/\\D/g, '');
    if (digitsOnly.length < 10 || digitsOnly.length > 15) {
      return { valid: false, error: 'Phone must have 10-15 digits' };
    }
    
    return { valid: true };
  }
}

// Usage in services
import { ValidationUtils } from '../utils/validators';

class UserService {
  createUser(userData: CreateUserData) {
    const emailValidation = ValidationUtils.validateEmail(userData.email);
    if (!emailValidation.valid) {
      throw new ValidationError(emailValidation.error);
    }
    
    const phoneValidation = ValidationUtils.validatePhone(userData.phone);
    if (!phoneValidation.valid) {
      throw new ValidationError(phoneValidation.error);
    }
    
    // Create user...
  }
}
```

---

### 3. High Cyclomatic Complexity (> 15)
**File**: `src/processors/orderProcessor.ts:89-178`
**Severity**: CRITICAL
**Code Smell**: Complex Method
**Maintainability**: POOR

**Overly Complex Method**:
```typescript
function calculateOrderTotal(order: Order): number {
  let total = 0;
  
  if (order.items && order.items.length > 0) {
    for (let i = 0; i < order.items.length; i++) {
      if (order.items[i].active) {
        if (order.items[i].type === 'product') {
          if (order.items[i].category === 'electronics') {
            if (order.items[i].warranty) {
              total += order.items[i].price * 1.1;
            } else {
              total += order.items[i].price;
            }
            if (order.items[i].hasDiscount) {
              if (order.customer.isPremium) {
                total *= 0.85; // 15% discount
              } else {
                total *= 0.9;  // 10% discount
              }
            }
          } else if (order.items[i].category === 'books') {
            total += order.items[i].price;
            if (order.items[i].isDigital) {
              total *= 0.95; // 5% discount for digital
            }
          } else {
            total += order.items[i].price;
          }
        } else if (order.items[i].type === 'service') {
          if (order.items[i].duration > 12) {
            total += order.items[i].price * 0.9; // Long-term discount
          } else {
            total += order.items[i].price;
          }
        }
      }
    }
    
    if (order.shippingMethod === 'express') {
      total += 15;
    } else if (order.shippingMethod === 'standard') {
      total += 5;
    }
    
    if (order.customer.country !== 'US') {
      total += 10; // International fee
    }
    
    if (total > 100 && order.customer.isPremium) {
      total *= 0.95; // Premium customer discount
    }
  }
  
  return total;
}
```

**Cyclomatic Complexity**: 16 (too high!)
**Problems**: Hard to understand, test, and maintain

**Refactored Solution**: Extract Methods + Strategy Pattern
```typescript
class OrderCalculator {
  calculateOrderTotal(order: Order): number {
    if (!this.hasValidItems(order)) return 0;
    
    const itemsTotal = this.calculateItemsTotal(order.items, order.customer);
    const shippingCost = this.calculateShippingCost(order.shippingMethod);
    const internationalFee = this.calculateInternationalFee(order.customer);
    const premiumDiscount = this.calculatePremiumDiscount(itemsTotal, order.customer);
    
    return itemsTotal + shippingCost + internationalFee - premiumDiscount;
  }
  
  private hasValidItems(order: Order): boolean {
    return order.items && order.items.length > 0;
  }
  
  private calculateItemsTotal(items: OrderItem[], customer: Customer): number {
    return items
      .filter(item => item.active)
      .reduce((total, item) => total + this.calculateItemPrice(item, customer), 0);
  }
  
  private calculateItemPrice(item: OrderItem, customer: Customer): number {
    const calculator = this.getItemCalculator(item.type);
    return calculator.calculate(item, customer);
  }
  
  private getItemCalculator(type: string): ItemCalculator {
    const calculators = {
      'product': new ProductCalculator(),
      'service': new ServiceCalculator()
    };
    return calculators[type] || new DefaultCalculator();
  }
}

// Strategy pattern for different item types
abstract class ItemCalculator {
  abstract calculate(item: OrderItem, customer: Customer): number;
}

class ProductCalculator extends ItemCalculator {
  calculate(item: OrderItem, customer: Customer): number {
    let price = item.price;
    
    if (item.category === 'electronics' && item.warranty) {
      price *= 1.1;
    }
    
    if (item.hasDiscount) {
      const discountRate = customer.isPremium ? 0.85 : 0.9;
      price *= discountRate;
    }
    
    if (item.category === 'books' && item.isDigital) {
      price *= 0.95;
    }
    
    return price;
  }
}

class ServiceCalculator extends ItemCalculator {
  calculate(item: OrderItem, customer: Customer): number {
    return item.duration > 12 ? item.price * 0.9 : item.price;
  }
}
```

**Benefits**:
- ✅ Reduced Complexity: Each method has single responsibility
- ✅ Testability: Easy to unit test individual calculations
- ✅ Extensibility: Easy to add new item types
- ✅ Readability: Clear, self-documenting code
```

### MAJOR QUALITY ISSUES SECTION
```markdown
## ❌ MAJOR CODE QUALITY ISSUES

### 1. Tight Coupling (Dependency Inversion Violation)
**File**: `src/services/emailService.ts:23-67`
**Severity**: MAJOR
**SOLID Violation**: Dependency Inversion Principle
**Issue**: High-level module depends on low-level implementation

**Tightly Coupled Code**:
```typescript
class EmailService {
  sendEmail(to: string, subject: string, body: string) {
    // Direct dependency on concrete implementation
    const nodemailer = require('nodemailer');
    const transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: { user: '<EMAIL>', pass: 'password' }
    });
    
    return transporter.sendMail({ to, subject, text: body });
  }
}
```

**Refactored with Dependency Injection**:
```typescript
interface EmailProvider {
  sendEmail(message: EmailMessage): Promise<EmailResult>;
}

class GmailProvider implements EmailProvider {
  async sendEmail(message: EmailMessage): Promise<EmailResult> {
    // Gmail-specific implementation
  }
}

class EmailService {
  constructor(private emailProvider: EmailProvider) {}
  
  async sendEmail(to: string, subject: string, body: string): Promise<EmailResult> {
    const message = new EmailMessage(to, subject, body);
    return await this.emailProvider.sendEmail(message);
  }
}
```

---

### 2. Magic Numbers and Strings
**Files**: Multiple service files
**Severity**: MAJOR
**Issue**: Hard-coded values reduce maintainability

**Magic Numbers Example**:
```typescript
function calculateLateFee(daysLate: number): number {
  if (daysLate > 30) return 50;      // Magic number!
  if (daysLate > 7) return 10;       // Magic number!
  return 0;
}
```

**Constants Solution**:
```typescript
const LATE_FEE_CONFIG = {
  EXTENDED_LATE_DAYS: 30,
  EXTENDED_LATE_FEE: 50,
  STANDARD_LATE_DAYS: 7,
  STANDARD_LATE_FEE: 10
} as const;

function calculateLateFee(daysLate: number): number {
  if (daysLate > LATE_FEE_CONFIG.EXTENDED_LATE_DAYS) {
    return LATE_FEE_CONFIG.EXTENDED_LATE_FEE;
  }
  if (daysLate > LATE_FEE_CONFIG.STANDARD_LATE_DAYS) {
    return LATE_FEE_CONFIG.STANDARD_LATE_FEE;
  }
  return 0;
}
```
```

### MINOR ISSUES & IMPROVEMENTS
```markdown
## ⚠️ MINOR CODE QUALITY ISSUES

### 1. Inconsistent Naming Conventions
**Files**: Multiple files across codebase
**Impact**: Reduced code readability and maintainability
**Fix**: Establish and enforce consistent naming standards

### 2. Missing Type Annotations
**Files**: JavaScript files without TypeScript
**Impact**: Reduced type safety and IDE support
**Fix**: Migrate to TypeScript with strict type checking

### 3. Long Parameter Lists
**Files**: Constructor and method signatures
**Impact**: Difficult to use and maintain
**Fix**: Use parameter objects or builder patterns

## 💡 CODE QUALITY IMPROVEMENTS

### Best Practices Implementation
1. **SOLID Principles**: Implement all five SOLID principles consistently
2. **Design Patterns**: Use appropriate design patterns for common problems
3. **Clean Code**: Follow clean code principles (Uncle Bob)
4. **Code Reviews**: Implement peer code review process

### Tool Integration
1. **Static Analysis**: ESLint, SonarQube for automated quality checks
2. **Complexity Metrics**: Cyclomatic complexity monitoring
3. **Code Coverage**: Minimum 80% test coverage requirement
4. **Documentation**: JSDoc for all public APIs

### Refactoring Strategy
1. **Boy Scout Rule**: Leave code better than you found it
2. **Incremental Refactoring**: Small, safe refactoring steps
3. **Test-Driven Refactoring**: Ensure tests before refactoring
4. **Legacy Code**: Gradual modernization of legacy components
```

## 🔍 CODE QUALITY ANALYSIS METHODOLOGY

### Quality Assessment Framework
1. **Static Analysis**: Code structure, complexity, patterns
2. **Design Review**: Architecture, patterns, SOLID compliance
3. **Maintainability**: How easy is the code to change and extend?
4. **Readability**: Can new team members understand the code?

### Metrics and Thresholds
**Complexity Metrics**:
- Cyclomatic Complexity: < 10 (good), > 15 (refactor needed)
- Method Length: < 20 lines (good), > 50 (refactor needed)
- Class Size: < 200 lines (good), > 500 (refactor needed)
- Parameter Count: < 4 (good), > 7 (refactor needed)

**Quality Gates**:
- Code Coverage: > 80%
- Duplicate Code: < 5%
- Technical Debt Ratio: < 20%
- Maintainability Index: > 70

### Refactoring Priorities
1. **Critical**: Security issues, performance bottlenecks
2. **High**: SOLID violations, complex methods
3. **Medium**: Code smells, naming issues
4. **Low**: Style inconsistencies, minor optimizations

## ⚠️ WICHTIGE CODE QUALITY REGELN

1. **SOLID First**: SOLID Principles sind nicht optional
2. **DRY Compliance**: Duplicate Code muss eliminiert werden  
3. **Complexity Control**: Cyclomatic Complexity unter 10 halten
4. **Readable Code**: Code muss selbst-dokumentierend sein
5. **Design Patterns**: Patterns verwenden wo angebracht, nicht over-engineer
6. **Technical Debt**: Technical Debt kontinuierlich abbauen

Führe eine systematische, tiefgreifende Code Quality Analyse durch mit Fokus auf SOLID Principles, Code Smells und praktische Refactoring-Empfehlungen."""

        # Add context-specific information
        files_section = ""
        if quality_context.get("quality_files"):
            files_section = f"""

## 📁 CODE QUALITY DATEIEN FÜR ANALYSE

Analysiere diese Dateien auf Code Quality Issues:
{chr(10).join(f"- {file}" for file in quality_context["quality_files"])}
"""

        class_section = ""
        if quality_context.get("class_files") and self.check_design_patterns:
            class_section = f"""

## 🏗️ CLASS/SERVICE DATEIEN (DESIGN PATTERNS)

Prüfe diese Dateien auf Design Pattern Usage und SOLID Compliance:
{chr(10).join(f"- {file}" for file in quality_context["class_files"])}
"""

        utility_section = ""
        if quality_context.get("utility_files"):
            utility_section = f"""

## 🔧 UTILITY DATEIEN (DUPLICATION CHECK)

Prüfe diese Utility-Dateien auf Code-Duplikation:
{chr(10).join(f"- {file}" for file in quality_context["utility_files"])}
"""

        config_section = ""
        if quality_context.get("config_files"):
            config_section = f"""

## ⚙️ KONFIGURATIONSDATEIEN

Prüfe diese Config-Dateien auf Magic Numbers/Strings:
{chr(10).join(f"- {file}" for file in quality_context["config_files"])}
"""

        context_section = f"""

## 🎯 CODE QUALITY ANALYSIS CONTEXT

**Branch**: {quality_context.get('branch_name', 'unknown')}
**Ticket**: {quality_context.get('ticket_id', 'unknown')}
**Working Directory**: {quality_context.get('working_path', '.')}
**Files to Analyze**: {len(quality_context.get('changed_files', []))} files
**Complexity Threshold**: {self.complexity_threshold}
**Duplication Threshold**: {self.duplication_threshold} lines
**Method Length Threshold**: {self.method_length_threshold} lines
**SOLID Principles Check**: {'✅ Enabled' if self.check_solid_principles else '❌ Disabled'}
**Design Patterns Check**: {'✅ Enabled' if self.check_design_patterns else '❌ Disabled'}
"""

        return base_prompt + context_section + files_section + class_section + utility_section + config_section

    async def _process_code_quality_analysis(self, analysis_result: Dict[str, Any], quality_context: Dict[str, Any]) -> str:
        """Process and structure the code quality analysis result."""
        
        # Add metadata and summary to the result
        metadata_header = f"""# 🎨 CODE QUALITY ANALYSIS REPORT

**Agent**: {self.display_name} ({self.agent_id})
**Analysis Date**: {datetime.now().isoformat()}
**Files Analyzed**: {len(quality_context.get('quality_files', []))}
**Class/Service Files**: {len(quality_context.get('class_files', []))}
**Utility Files**: {len(quality_context.get('utility_files', []))}
**Configuration Files**: {len(quality_context.get('config_files', []))}
**Complexity Threshold**: {self.complexity_threshold}
**Duplication Threshold**: {self.duplication_threshold} lines
**Method Length Threshold**: {self.method_length_threshold} lines
**SOLID Principles Check**: {'Enabled' if self.check_solid_principles else 'Disabled'}
**Design Patterns Check**: {'Enabled' if self.check_design_patterns else 'Disabled'}

---

"""
        
        # Extract the actual analysis content from the response
        content = ""
        if isinstance(analysis_result, dict):
            # Try to extract content from various possible keys
            content = (analysis_result.get('content') or 
                      analysis_result.get('response') or 
                      analysis_result.get('result') or 
                      str(analysis_result))
        else:
            content = str(analysis_result)
        
        return metadata_header + content

    def get_agent_info(self) -> Dict[str, Any]:
        """Return agent information for factory registration."""
        return {
            "agent_type": self.agent_type,
            "display_name": self.display_name,
            "description": self.description,
            "capabilities": [
                "Code Smells Detection",
                "SOLID Principles Validation",
                "Design Pattern Analysis",
                "Maintainability Assessment",
                "Complexity Analysis", 
                "Best Practices Validation",
                "Technical Debt Identification"
            ],
            "supported_file_types": [".py", ".js", ".ts", ".tsx", ".jsx", ".java", ".cpp", ".c", ".cs", ".rb", ".go", ".rs", ".php", ".scala", ".kt"],
            "estimated_execution_time": "8-12 minutes",
            "parallel_compatible": True
        }