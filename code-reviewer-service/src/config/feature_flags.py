"""
Feature Flag System for Multi-Agent Architecture Migration
Enables gradual rollout from sequential to parallel execution with safe rollback.
"""

import logging
from enum import Enum
from typing import Dict, Any, Optional, Set, List
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
from pathlib import Path
import asyncio
from functools import wraps

from pydantic import BaseModel, <PERSON>, validator
from pydantic_settings import BaseSettings


logger = logging.getLogger(__name__)


class ExecutionMode(str, Enum):
    """Execution modes for code review system."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    HYBRID = "hybrid"  # Mix of both for A/B testing


class RolloutStrategy(str, Enum):
    """Rollout strategies for feature deployment."""
    INSTANT = "instant"
    GRADUAL = "gradual"
    CANARY = "canary"
    BLUE_GREEN = "blue_green"
    A_B_TEST = "a_b_test"


class FeatureFlagStatus(str, Enum):
    """Status of a feature flag."""
    DISABLED = "disabled"
    ENABLED = "enabled"
    TESTING = "testing"
    DEPRECATED = "deprecated"
    ROLLBACK = "rollback"


@dataclass
class RolloutConfig:
    """Configuration for gradual rollout."""
    strategy: RolloutStrategy
    percentage: float = 0.0  # 0-100
    target_percentage: float = 100.0
    increment_step: float = 10.0
    increment_interval_minutes: int = 60
    rollback_threshold_error_rate: float = 5.0  # %
    canary_user_groups: Optional[Set[str]] = None
    exclude_user_groups: Optional[Set[str]] = None


class FeatureFlag(BaseModel):
    """Individual feature flag configuration."""
    
    name: str
    description: str
    status: FeatureFlagStatus = FeatureFlagStatus.DISABLED
    execution_mode: ExecutionMode = ExecutionMode.SEQUENTIAL
    
    # Rollout configuration
    rollout_config: Optional[Dict[str, Any]] = None
    
    # Conditional rules
    enabled_for_users: Optional[Set[str]] = None
    enabled_for_groups: Optional[Set[str]] = None
    enabled_for_environments: Optional[Set[str]] = None
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    created_by: Optional[str] = None
    
    # Monitoring
    usage_count: int = 0
    error_count: int = 0
    success_count: int = 0
    
    @validator('execution_mode')
    def validate_execution_mode(cls, v):
        if isinstance(v, str):
            return ExecutionMode(v)
        return v
    
    @validator('status')
    def validate_status(cls, v):
        if isinstance(v, str):
            return FeatureFlagStatus(v)
        return v
    
    def is_enabled_for_context(self, context: Dict[str, Any]) -> bool:
        """Check if flag is enabled for given context."""
        if self.status == FeatureFlagStatus.DISABLED:
            return False
        
        if self.status == FeatureFlagStatus.ROLLBACK:
            return False
        
        # Check environment restrictions
        if self.enabled_for_environments:
            environment = context.get("environment", "development")
            if environment not in self.enabled_for_environments:
                return False
        
        # Check user restrictions
        if self.enabled_for_users:
            user_id = context.get("user_id")
            if user_id and user_id not in self.enabled_for_users:
                return False
        
        # Check group restrictions
        if self.enabled_for_groups:
            user_groups = context.get("user_groups", set())
            if not user_groups.intersection(self.enabled_for_groups):
                return False
        
        # Check rollout percentage
        if self.rollout_config:
            rollout = RolloutConfig(**self.rollout_config) if self.rollout_config else None
            if rollout and rollout.strategy == RolloutStrategy.GRADUAL:
                # Simple percentage-based rollout using user_id hash
                user_id = context.get("user_id", context.get("session_id", "default"))
                user_hash = hash(user_id) % 100
                return user_hash < rollout.percentage
            
            elif rollout and rollout.strategy == RolloutStrategy.CANARY:
                user_groups = context.get("user_groups", set())
                return bool(rollout.canary_user_groups and 
                           user_groups.intersection(rollout.canary_user_groups))
        
        return self.status == FeatureFlagStatus.ENABLED


class FeatureFlagSettings(BaseSettings):
    """Feature flag specific settings."""
    
    # Storage
    feature_flags_file: str = Field(default="feature_flags.json", env="FEATURE_FLAGS_FILE")
    feature_flags_redis_key: str = Field(default="feature_flags", env="FEATURE_FLAGS_REDIS_KEY")
    use_redis_storage: bool = Field(default=False, env="USE_REDIS_FEATURE_FLAGS")
    
    # Rollout automation
    enable_automatic_rollout: bool = Field(default=False, env="ENABLE_AUTOMATIC_ROLLOUT")
    rollout_check_interval_minutes: int = Field(default=30, env="ROLLOUT_CHECK_INTERVAL")
    
    # Safety
    enable_circuit_breaker: bool = Field(default=True, env="ENABLE_FEATURE_FLAG_CIRCUIT_BREAKER")
    circuit_breaker_error_threshold: float = Field(default=10.0, env="CIRCUIT_BREAKER_ERROR_THRESHOLD")
    
    class Config:
        env_file = ".env"


class FeatureFlagManager:
    """Manages feature flags for the multi-agent architecture migration."""
    
    def __init__(self, settings: Optional[FeatureFlagSettings] = None):
        self.settings = settings or FeatureFlagSettings()
        self.flags: Dict[str, FeatureFlag] = {}
        self._last_loaded: Optional[datetime] = None
        self._auto_rollout_task: Optional[asyncio.Task] = None
        
        # Initialize default flags
        self._initialize_default_flags()
        
        # Load from storage
        self.load_flags()
        
        # Start automatic rollout if enabled
        if self.settings.enable_automatic_rollout:
            self._start_automatic_rollout()
    
    def _initialize_default_flags(self):
        """Initialize default feature flags for migration."""
        
        # Main execution mode flag
        self.flags["multi_agent_execution"] = FeatureFlag(
            name="multi_agent_execution",
            description="Enable parallel multi-agent execution instead of sequential",
            status=FeatureFlagStatus.DISABLED,
            execution_mode=ExecutionMode.SEQUENTIAL,
            enabled_for_environments={"development", "testing"},
            rollout_config={
                "strategy": RolloutStrategy.GRADUAL.value,
                "percentage": 0.0,
                "target_percentage": 100.0,
                "increment_step": 10.0,
                "increment_interval_minutes": 60,
                "rollback_threshold_error_rate": 5.0
            }
        )
        
        # Individual agent flags for granular control
        agent_types = [
            "acceptance_criteria", "bug_detection", "security_analysis",
            "code_quality", "performance_analysis", "architectural_analysis", "summary_agent"
        ]
        
        for agent_type in agent_types:
            self.flags[f"parallel_{agent_type}"] = FeatureFlag(
                name=f"parallel_{agent_type}",
                description=f"Enable parallel execution for {agent_type} agent",
                status=FeatureFlagStatus.DISABLED,
                execution_mode=ExecutionMode.PARALLEL,
                enabled_for_environments={"development", "testing"}
            )
        
        # WebSocket integration flag
        self.flags["websocket_progress_updates"] = FeatureFlag(
            name="websocket_progress_updates",
            description="Enable real-time WebSocket progress updates",
            status=FeatureFlagStatus.ENABLED,
            execution_mode=ExecutionMode.PARALLEL,
            enabled_for_environments={"development", "testing", "staging", "production"}
        )
        
        # Performance monitoring flag
        self.flags["performance_monitoring"] = FeatureFlag(
            name="performance_monitoring",
            description="Enable detailed performance monitoring and metrics",
            status=FeatureFlagStatus.ENABLED,
            execution_mode=ExecutionMode.PARALLEL
        )
        
        # Circuit breaker flag
        self.flags["circuit_breaker"] = FeatureFlag(
            name="circuit_breaker",
            description="Enable circuit breaker for agent failures",
            status=FeatureFlagStatus.ENABLED,
            execution_mode=ExecutionMode.PARALLEL
        )
        
        # A/B testing flag
        self.flags["ab_test_parallel_vs_sequential"] = FeatureFlag(
            name="ab_test_parallel_vs_sequential",
            description="A/B test parallel vs sequential execution",
            status=FeatureFlagStatus.TESTING,
            execution_mode=ExecutionMode.HYBRID,
            rollout_config={
                "strategy": RolloutStrategy.A_B_TEST.value,
                "percentage": 50.0  # 50/50 split
            }
        )
    
    def is_enabled(self, flag_name: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if a feature flag is enabled for the given context."""
        context = context or {}
        
        flag = self.flags.get(flag_name)
        if not flag:
            logger.warning(f"Feature flag '{flag_name}' not found, defaulting to False")
            return False
        
        try:
            enabled = flag.is_enabled_for_context(context)
            
            # Update usage metrics
            flag.usage_count += 1
            if enabled:
                flag.success_count += 1
            
            logger.debug(f"Feature flag '{flag_name}' evaluated to {enabled} for context: {context}")
            return enabled
            
        except Exception as e:
            logger.error(f"Error evaluating feature flag '{flag_name}': {e}")
            flag.error_count += 1
            
            # Circuit breaker logic
            if self.settings.enable_circuit_breaker:
                error_rate = (flag.error_count / max(flag.usage_count, 1)) * 100
                if error_rate > self.settings.circuit_breaker_error_threshold:
                    logger.warning(f"Circuit breaker triggered for flag '{flag_name}' (error rate: {error_rate:.1f}%)")
                    flag.status = FeatureFlagStatus.ROLLBACK
            
            return False
    
    def get_execution_mode(self, context: Optional[Dict[str, Any]] = None) -> ExecutionMode:
        """Get the current execution mode based on feature flags."""
        context = context or {}
        
        # Check main execution mode flag
        if self.is_enabled("multi_agent_execution", context):
            main_flag = self.flags["multi_agent_execution"]
            return main_flag.execution_mode
        
        # Check A/B testing flag
        if self.is_enabled("ab_test_parallel_vs_sequential", context):
            # Simple A/B split based on user_id hash
            user_id = context.get("user_id", context.get("session_id", "default"))
            return ExecutionMode.PARALLEL if hash(user_id) % 2 == 0 else ExecutionMode.SEQUENTIAL
        
        # Default to sequential for safety
        return ExecutionMode.SEQUENTIAL
    
    def should_use_parallel_agent(self, agent_type: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if specific agent should run in parallel mode."""
        context = context or {}
        
        # Check global execution mode first
        global_mode = self.get_execution_mode(context)
        if global_mode == ExecutionMode.SEQUENTIAL:
            return False
        
        # Check agent-specific flag
        agent_flag = f"parallel_{agent_type}"
        return self.is_enabled(agent_flag, context)
    
    def enable_flag(self, flag_name: str, context: Optional[Dict[str, Any]] = None):
        """Enable a feature flag."""
        if flag_name in self.flags:
            self.flags[flag_name].status = FeatureFlagStatus.ENABLED
            self.flags[flag_name].updated_at = datetime.now()
            self.save_flags()
            logger.info(f"Feature flag '{flag_name}' enabled")
        else:
            logger.error(f"Feature flag '{flag_name}' not found")
    
    def disable_flag(self, flag_name: str):
        """Disable a feature flag."""
        if flag_name in self.flags:
            self.flags[flag_name].status = FeatureFlagStatus.DISABLED
            self.flags[flag_name].updated_at = datetime.now()
            self.save_flags()
            logger.info(f"Feature flag '{flag_name}' disabled")
        else:
            logger.error(f"Feature flag '{flag_name}' not found")
    
    def rollback_flag(self, flag_name: str, reason: str = "Manual rollback"):
        """Rollback a feature flag due to issues."""
        if flag_name in self.flags:
            self.flags[flag_name].status = FeatureFlagStatus.ROLLBACK
            self.flags[flag_name].updated_at = datetime.now()
            self.save_flags()
            logger.warning(f"Feature flag '{flag_name}' rolled back: {reason}")
        else:
            logger.error(f"Feature flag '{flag_name}' not found")
    
    def update_rollout_percentage(self, flag_name: str, percentage: float):
        """Update rollout percentage for gradual rollout."""
        if flag_name in self.flags:
            flag = self.flags[flag_name]
            if flag.rollout_config:
                flag.rollout_config["percentage"] = min(100.0, max(0.0, percentage))
                flag.updated_at = datetime.now()
                self.save_flags()
                logger.info(f"Rollout percentage for '{flag_name}' updated to {percentage}%")
    
    def get_flag_status(self, flag_name: str) -> Dict[str, Any]:
        """Get detailed status of a feature flag."""
        flag = self.flags.get(flag_name)
        if not flag:
            return {
                "name": flag_name,
                "exists": False,
                "error": f"Flag '{flag_name}' not found"
            }
        
        return {
            "name": flag.name,
            "description": flag.description,
            "status": flag.status.value,
            "execution_mode": flag.execution_mode.value,
            "usage_count": flag.usage_count,
            "success_count": flag.success_count,
            "error_count": flag.error_count,
            "error_rate": (flag.error_count / max(flag.usage_count, 1)) * 100,
            "rollout_config": flag.rollout_config,
            "created_at": flag.created_at.isoformat(),
            "updated_at": flag.updated_at.isoformat()
        }
    
    def get_all_flags_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all feature flags."""
        return {name: self.get_flag_status(name) for name in self.flags.keys()}
    
    def load_flags(self):
        """Load feature flags from storage."""
        try:
            if self.settings.use_redis_storage:
                self._load_from_redis()
            else:
                self._load_from_file()
            
            self._last_loaded = datetime.now()
            logger.info("Feature flags loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading feature flags: {e}")
    
    def save_flags(self):
        """Save feature flags to storage."""
        try:
            if self.settings.use_redis_storage:
                self._save_to_redis()
            else:
                self._save_to_file()
            
            logger.debug("Feature flags saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving feature flags: {e}")
    
    def _load_from_file(self):
        """Load flags from JSON file."""
        file_path = Path(self.settings.feature_flags_file)
        if file_path.exists():
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            for flag_name, flag_data in data.items():
                if flag_name in self.flags:
                    # Update existing flag
                    flag = self.flags[flag_name]
                    for key, value in flag_data.items():
                        if hasattr(flag, key):
                            setattr(flag, key, value)
    
    def _save_to_file(self):
        """Save flags to JSON file."""
        data = {}
        for flag_name, flag in self.flags.items():
            data[flag_name] = flag.dict()
        
        file_path = Path(self.settings.feature_flags_file)
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)
    
    def _load_from_redis(self):
        """Load flags from Redis (placeholder for Redis implementation)."""
        # TODO: Implement Redis storage
        logger.warning("Redis storage not yet implemented, falling back to file storage")
        self._load_from_file()
    
    def _save_to_redis(self):
        """Save flags to Redis (placeholder for Redis implementation)."""
        # TODO: Implement Redis storage
        logger.warning("Redis storage not yet implemented, falling back to file storage")
        self._save_to_file()
    
    def _start_automatic_rollout(self):
        """Start automatic rollout task."""
        if not self._auto_rollout_task:
            self._auto_rollout_task = asyncio.create_task(self._automatic_rollout_loop())
            logger.info("Automatic rollout task started")
    
    async def _automatic_rollout_loop(self):
        """Automatic rollout loop for gradual deployments."""
        while True:
            try:
                await asyncio.sleep(self.settings.rollout_check_interval_minutes * 60)
                
                for flag_name, flag in self.flags.items():
                    if (flag.status == FeatureFlagStatus.ENABLED and 
                        flag.rollout_config and 
                        flag.rollout_config.get("strategy") == RolloutStrategy.GRADUAL.value):
                        
                        await self._process_gradual_rollout(flag_name, flag)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in automatic rollout loop: {e}")
    
    async def _process_gradual_rollout(self, flag_name: str, flag: FeatureFlag):
        """Process gradual rollout for a flag."""
        if flag.rollout_config:
            rollout = RolloutConfig(**flag.rollout_config)
        else:
            return  # No rollout config, skip processing
        
        # Check error rate
        if flag.usage_count > 0:
            error_rate = (flag.error_count / flag.usage_count) * 100
            if error_rate > rollout.rollback_threshold_error_rate:
                logger.warning(f"Rolling back '{flag_name}' due to high error rate: {error_rate:.1f}%")
                self.rollback_flag(flag_name, f"Automatic rollback due to {error_rate:.1f}% error rate")
                return
        
        # Increment rollout percentage
        current_percentage = rollout.percentage
        if current_percentage < rollout.target_percentage:
            new_percentage = min(
                rollout.target_percentage,
                current_percentage + rollout.increment_step
            )
            self.update_rollout_percentage(flag_name, new_percentage)
            logger.info(f"Automatic rollout: '{flag_name}' increased to {new_percentage}%")


# Global feature flag manager instance
_feature_flag_manager: Optional[FeatureFlagManager] = None


def get_feature_flag_manager() -> FeatureFlagManager:
    """Get the global feature flag manager instance."""
    global _feature_flag_manager
    if _feature_flag_manager is None:
        _feature_flag_manager = FeatureFlagManager()
    return _feature_flag_manager


def is_feature_enabled(flag_name: str, context: Optional[Dict[str, Any]] = None) -> bool:
    """Convenience function to check if a feature is enabled."""
    return get_feature_flag_manager().is_enabled(flag_name, context)


def get_execution_mode(context: Optional[Dict[str, Any]] = None) -> ExecutionMode:
    """Convenience function to get execution mode."""
    return get_feature_flag_manager().get_execution_mode(context)


def feature_flag(flag_name: str, default_return=None):
    """Decorator to conditionally execute functions based on feature flags."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Try to extract context from arguments
            context = {}
            if args and hasattr(args[0], '__dict__'):
                # Look for common context attributes
                obj = args[0]
                if hasattr(obj, 'user_id'):
                    context['user_id'] = obj.user_id
                if hasattr(obj, 'environment'):
                    context['environment'] = obj.environment
            
            if is_feature_enabled(flag_name, context):
                return func(*args, **kwargs)
            else:
                logger.debug(f"Function '{func.__name__}' skipped due to disabled feature flag '{flag_name}'")
                return default_return
        
        return wrapper
    return decorator


async def async_feature_flag(flag_name: str, default_return=None):
    """Async decorator for feature flags."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            context = {}
            if args and hasattr(args[0], '__dict__'):
                obj = args[0]
                if hasattr(obj, 'user_id'):
                    context['user_id'] = obj.user_id
                if hasattr(obj, 'environment'):
                    context['environment'] = obj.environment
            
            if is_feature_enabled(flag_name, context):
                return await func(*args, **kwargs)
            else:
                logger.debug(f"Async function '{func.__name__}' skipped due to disabled feature flag '{flag_name}'")
                return default_return
        
        return wrapper
    return decorator