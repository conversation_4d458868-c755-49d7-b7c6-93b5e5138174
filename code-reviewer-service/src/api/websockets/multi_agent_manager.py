"""
Multi-Agent WebSocket Manager
Real-time progress updates for parallel agent execution
"""

import asyncio
import json
import time
from typing import Dict, List, Set, Optional, Any
from uuid import UUID
from enum import Enum

import structlog
from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel, Field, ConfigDict

from src.config.settings import get_settings
from src.utils.monitoring import get_tracer, log_performance_metrics

logger = structlog.get_logger()
settings = get_settings()


class WebSocketEventType(str, Enum):
    """WebSocket event types for multi-agent progress"""
    
    # Connection events
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    
    # Review session events
    REVIEW_STARTED = "review_started"
    REVIEW_COMPLETED = "review_completed"
    REVIEW_FAILED = "review_failed"
    REVIEW_CANCELLED = "review_cancelled"
    
    # Execution events
    EXECUTION_STARTED = "execution_started"
    EXECUTION_COMPLETED = "execution_completed"
    EXECUTION_ERROR = "execution_error"
    
    # Agent-specific events
    AGENT_STARTED = "agent_started"
    AGENT_PROGRESS = "agent_progress"
    AGENT_COMPLETED = "agent_completed"
    AGENT_FAILED = "agent_failed"
    AGENT_TIMEOUT = "agent_timeout"
    
    # System events
    ERROR = "error"
    HEARTBEAT = "heartbeat"


class WebSocketMessage(BaseModel):
    """WebSocket message structure"""
    
    event_type: WebSocketEventType
    timestamp: float = Field(default_factory=time.time)
    review_id: Optional[UUID] = None
    agent_type: Optional[str] = None
    data: Dict[str, Any] = Field(default_factory=dict)
    message: Optional[str] = None


class ConnectionInfo(BaseModel):
    """WebSocket connection information"""
    
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    websocket: WebSocket
    user_id: Optional[str] = None
    session_id: str
    connected_at: float = Field(default_factory=time.time)
    subscribed_reviews: Set[UUID] = Field(default_factory=set)
    last_heartbeat: float = Field(default_factory=time.time)


class MultiAgentWebSocketManager:
    """
    WebSocket manager for multi-agent progress tracking
    Handles real-time updates for parallel agent execution
    """
    
    def __init__(self):
        self.connections: Dict[str, ConnectionInfo] = {}  # session_id -> connection
        self.review_subscribers: Dict[UUID, Set[str]] = {}  # review_id -> session_ids
        self.tracer = get_tracer(__name__)
        self._heartbeat_interval = settings.websocket_ping_interval
        self._heartbeat_timeout = settings.websocket_ping_timeout
        self._heartbeat_task: Optional[asyncio.Task] = None
    
    async def connect(
        self,
        websocket: WebSocket,
        session_id: str,
        user_id: Optional[str] = None
    ) -> None:
        """
        Accept new WebSocket connection
        
        Args:
            websocket: FastAPI WebSocket instance
            session_id: Unique session identifier
            user_id: Optional user identifier for authentication
        """
        
        with self.tracer.start_as_current_span("websocket_connect") as span:
            span.set_attribute("session_id", session_id)
            span.set_attribute("user_id", user_id or "anonymous")
            
            try:
                await websocket.accept()
                
                # Store connection info
                connection_info = ConnectionInfo(
                    websocket=websocket,
                    user_id=user_id,
                    session_id=session_id
                )
                
                self.connections[session_id] = connection_info
                
                # Start heartbeat if first connection
                if len(self.connections) == 1:
                    await self._start_heartbeat()
                
                logger.info(
                    "WebSocket connection established",
                    session_id=session_id,
                    user_id=user_id,
                    total_connections=len(self.connections)
                )
                
                # Log metrics
                log_performance_metrics(
                    operation="websocket_connect",
                    duration_ms=0,
                    success=True,
                    additional_data={
                        "session_id": session_id,
                        "total_connections": len(self.connections)
                    }
                )
                
            except Exception as e:
                logger.error(
                    "Failed to establish WebSocket connection",
                    session_id=session_id,
                    error=str(e),
                    exc_info=True
                )
                raise
    
    async def disconnect(self, session_id: str) -> None:
        """
        Handle WebSocket disconnection
        
        Args:
            session_id: Session identifier to disconnect
        """
        
        with self.tracer.start_as_current_span("websocket_disconnect") as span:
            span.set_attribute("session_id", session_id)
            
            try:
                connection_info = self.connections.get(session_id)
                if not connection_info:
                    return
                
                # Unsubscribe from all reviews
                for review_id in connection_info.subscribed_reviews.copy():
                    await self.leave_review_room(session_id, review_id)
                
                # Remove connection
                del self.connections[session_id]
                
                # Stop heartbeat if no connections
                if not self.connections and self._heartbeat_task:
                    self._heartbeat_task.cancel()
                    self._heartbeat_task = None
                
                logger.info(
                    "WebSocket connection closed",
                    session_id=session_id,
                    remaining_connections=len(self.connections)
                )
                
                log_performance_metrics(
                    operation="websocket_disconnect",
                    duration_ms=0,
                    success=True,
                    additional_data={
                        "session_id": session_id,
                        "remaining_connections": len(self.connections)
                    }
                )
                
            except Exception as e:
                logger.error(
                    "Error during WebSocket disconnection",
                    session_id=session_id,
                    error=str(e),
                    exc_info=True
                )
    
    async def join_review_room(self, session_id: str, review_id: UUID) -> None:
        """
        Subscribe connection to review progress updates
        
        Args:
            session_id: Session identifier
            review_id: Review identifier to subscribe to
        """
        
        connection_info = self.connections.get(session_id)
        if not connection_info:
            logger.warning("Attempt to join review room with invalid session", session_id=session_id)
            return
        
        # Add to review subscribers
        if review_id not in self.review_subscribers:
            self.review_subscribers[review_id] = set()
        
        self.review_subscribers[review_id].add(session_id)
        connection_info.subscribed_reviews.add(review_id)
        
        # Confirm subscription
        await self._send_to_connection(
            session_id,
            WebSocketMessage(
                event_type=WebSocketEventType.CONNECTED,
                review_id=review_id,
                message=f"Subscribed to review {review_id}",
                data={"review_id": str(review_id)}
            )
        )
        
        logger.info(
            "Session joined review room",
            session_id=session_id,
            review_id=str(review_id),
            total_subscribers=len(self.review_subscribers[review_id])
        )
    
    async def leave_review_room(self, session_id: str, review_id: UUID) -> None:
        """
        Unsubscribe connection from review progress updates
        
        Args:
            session_id: Session identifier
            review_id: Review identifier to unsubscribe from
        """
        
        connection_info = self.connections.get(session_id)
        if connection_info:
            connection_info.subscribed_reviews.discard(review_id)
        
        # Remove from review subscribers
        if review_id in self.review_subscribers:
            self.review_subscribers[review_id].discard(session_id)
            
            # Clean up empty review rooms
            if not self.review_subscribers[review_id]:
                del self.review_subscribers[review_id]
        
        logger.info(
            "Session left review room",
            session_id=session_id,
            review_id=str(review_id)
        )
    
    async def broadcast_agent_event(
        self,
        event_type: WebSocketEventType,
        review_id: UUID,
        agent_type: str,
        data: Optional[Dict[str, Any]] = None,
        message: Optional[str] = None
    ) -> None:
        """
        Broadcast agent event to all subscribers of a review
        
        Args:
            event_type: Type of agent event
            review_id: Review identifier
            agent_type: Type of agent (security_analysis, bug_detection, etc.)
            data: Additional event data
            message: Optional event message
        """
        
        with self.tracer.start_as_current_span("websocket_broadcast_agent") as span:
            span.set_attribute("event_type", event_type.value)
            span.set_attribute("review_id", str(review_id))
            span.set_attribute("agent_type", agent_type)
            
            start_time = time.time()
            
            # Get subscribers for this review
            subscribers = self.review_subscribers.get(review_id, set())
            
            if not subscribers:
                logger.debug(
                    "No subscribers for agent event",
                    review_id=str(review_id),
                    agent_type=agent_type,
                    event_type=event_type.value
                )
                return
            
            # Prepare message
            ws_message = WebSocketMessage(
                event_type=event_type,
                review_id=review_id,
                agent_type=agent_type,
                data=data or {},
                message=message
            )
            
            # Broadcast to all subscribers
            broadcast_tasks = []
            for session_id in subscribers.copy():  # Copy to avoid modification during iteration
                task = self._send_to_connection(session_id, ws_message)
                broadcast_tasks.append(task)
            
            # Wait for all broadcasts to complete
            if broadcast_tasks:
                results = await asyncio.gather(*broadcast_tasks, return_exceptions=True)
                
                # Count successful/failed broadcasts
                successful = sum(1 for result in results if not isinstance(result, Exception))
                failed = len(results) - successful
                
                duration = time.time() - start_time
                
                span.set_attribute("subscribers_count", len(subscribers))
                span.set_attribute("successful_broadcasts", successful)
                span.set_attribute("failed_broadcasts", failed)
                
                logger.info(
                    "Agent event broadcasted",
                    event_type=event_type.value,
                    review_id=str(review_id),
                    agent_type=agent_type,
                    subscribers=len(subscribers),
                    successful=successful,
                    failed=failed,
                    duration_ms=round(duration * 1000, 2)
                )
                
                log_performance_metrics(
                    operation="websocket_broadcast",
                    duration_ms=duration * 1000,
                    success=failed == 0,
                    additional_data={
                        "event_type": event_type.value,
                        "subscribers": len(subscribers),
                        "successful": successful,
                        "failed": failed
                    }
                )
    
    async def broadcast_review_event(
        self,
        event_type: WebSocketEventType,
        review_id: UUID,
        data: Optional[Dict[str, Any]] = None,
        message: Optional[str] = None
    ) -> None:
        """
        Broadcast review-level event to all subscribers
        
        Args:
            event_type: Type of review event
            review_id: Review identifier
            data: Additional event data  
            message: Optional event message
        """
        
        await self.broadcast_agent_event(
            event_type=event_type,
            review_id=review_id,
            agent_type="system",  # System-level event
            data=data,
            message=message
        )
    
    async def _send_to_connection(
        self,
        session_id: str,
        message: WebSocketMessage
    ) -> None:
        """
        Send message to specific connection
        
        Args:
            session_id: Target session identifier
            message: Message to send
        """
        
        connection_info = self.connections.get(session_id)
        if not connection_info:
            logger.warning("Attempt to send to non-existent connection", session_id=session_id)
            return
        
        try:
            # Serialize message
            message_json = message.model_dump(mode='json')
            message_str = json.dumps(message_json, default=str)
            
            # Send via WebSocket
            await connection_info.websocket.send_text(message_str)
            
            # Update last activity
            connection_info.last_heartbeat = time.time()
            
        except WebSocketDisconnect:
            logger.info("WebSocket disconnected during send", session_id=session_id)
            await self.disconnect(session_id)
        except Exception as e:
            logger.error(
                "Failed to send WebSocket message",
                session_id=session_id,
                error=str(e),
                exc_info=True
            )
            # Don't disconnect on send errors - connection might still be valid
    
    async def _start_heartbeat(self) -> None:
        """Start heartbeat task to maintain connections"""
        
        if self._heartbeat_task and not self._heartbeat_task.done():
            return
        
        self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        logger.info("WebSocket heartbeat started")
    
    async def _heartbeat_loop(self) -> None:
        """Heartbeat loop to check connection health"""
        
        # Wait for initial heartbeat interval before starting
        # This gives connections time to establish properly
        await asyncio.sleep(self._heartbeat_interval)
        
        while self.connections:
            try:
                current_time = time.time()
                disconnected_sessions = []
                
                # Check all connections (copy dict to avoid modification during iteration)
                connections_copy = dict(self.connections)
                for session_id, connection_info in connections_copy.items():
                    # Skip if connection was removed during iteration
                    if session_id not in self.connections:
                        continue
                    time_since_heartbeat = current_time - connection_info.last_heartbeat
                    
                    if time_since_heartbeat > self._heartbeat_timeout:
                        # Connection timed out
                        logger.warning(
                            "WebSocket connection timed out",
                            session_id=session_id,
                            timeout_seconds=time_since_heartbeat
                        )
                        disconnected_sessions.append(session_id)
                    else:
                        # Send heartbeat
                        try:
                            await self._send_to_connection(
                                session_id,
                                WebSocketMessage(
                                    event_type=WebSocketEventType.HEARTBEAT,
                                    data={"server_time": current_time}
                                )
                            )
                        except Exception as e:
                            logger.error(
                                "Heartbeat send failed",
                                session_id=session_id,
                                error=str(e)
                            )
                            disconnected_sessions.append(session_id)
                
                # Clean up disconnected sessions
                for session_id in disconnected_sessions:
                    await self.disconnect(session_id)
                
                # Wait for next heartbeat interval
                await asyncio.sleep(self._heartbeat_interval)
                
            except asyncio.CancelledError:
                logger.info("Heartbeat loop cancelled")
                break
            except Exception as e:
                logger.error("Error in heartbeat loop", error=str(e), exc_info=True)
                await asyncio.sleep(self._heartbeat_interval)
        
        logger.info("WebSocket heartbeat stopped")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get WebSocket connection statistics"""
        
        total_connections = len(self.connections)
        total_reviews = len(self.review_subscribers)
        
        # Calculate connection distribution
        review_distribution = {}
        for review_id, subscribers in self.review_subscribers.items():
            review_distribution[str(review_id)] = len(subscribers)
        
        return {
            "total_connections": total_connections,
            "active_reviews": total_reviews,
            "review_distribution": review_distribution,
            "heartbeat_interval": self._heartbeat_interval,
            "heartbeat_timeout": self._heartbeat_timeout,
            "heartbeat_active": self._heartbeat_task is not None and not self._heartbeat_task.done()
        }


# Global WebSocket manager instance
websocket_manager = MultiAgentWebSocketManager()