"""Worktree configuration routes"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, <PERSON>
from typing import Dict, List
import logging

from src.services.worktree_config_service import WorktreeConfigService, WorktreeConfig

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/worktree", tags=["worktree"])

class WorktreeConfigRequest(BaseModel):
    """Request model for worktree configuration"""
    base_path: str = Field(..., description="Path to the master Git repository")
    user_id: str = Field(default="default", description="User ID for configuration")

class WorktreeConfigResponse(BaseModel):
    """Response model for worktree configuration"""
    base_path: str
    is_valid: bool
    last_validated: str
    user_id: str
    message: str = ""

class WorktreeValidationResponse(BaseModel):
    """Response model for path validation"""
    is_valid: bool
    message: str
    suggested_directories: List[Dict[str, str]] = []

@router.get("/config", response_model=WorktreeConfigResponse)
async def get_worktree_config(user_id: str = "default"):
    """Get current worktree configuration"""
    try:
        service = WorktreeConfigService()
        config = service.get_config(user_id)
        
        return WorktreeConfigResponse(
            base_path=config.base_path,
            is_valid=config.is_valid,
            last_validated=config.last_validated,
            user_id=config.user_id,
            message="Worktree configuration retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Error getting worktree config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/config/validate", response_model=WorktreeValidationResponse)
async def validate_worktree_path(request: WorktreeConfigRequest):
    """Validate a worktree master repository path"""
    try:
        service = WorktreeConfigService()
        is_valid, message = service.validate_path(request.base_path)
        
        # Get suggested directories if validation fails
        suggested_dirs = []
        if not is_valid:
            suggested_dirs = service.list_suggested_directories()
        
        return WorktreeValidationResponse(
            is_valid=is_valid,
            message=message,
            suggested_directories=suggested_dirs
        )
    except Exception as e:
        logger.error(f"Error validating worktree path: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/config", response_model=WorktreeConfigResponse)
async def save_worktree_config(request: WorktreeConfigRequest):
    """Save worktree configuration after validation"""
    try:
        service = WorktreeConfigService()
        
        # First validate the path
        is_valid, validation_message = service.validate_path(request.base_path)
        
        if not is_valid:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid repository path: {validation_message}"
            )
        
        # Save the configuration
        from datetime import datetime
        config = WorktreeConfig(
            base_path=request.base_path,
            is_valid=True,
            last_validated=datetime.now().isoformat(),
            user_id=request.user_id
        )
        
        success = service.save_config(config)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to save configuration")
        
        return WorktreeConfigResponse(
            base_path=config.base_path,
            is_valid=config.is_valid,
            last_validated=config.last_validated,
            user_id=config.user_id,
            message=f"Worktree configuration saved successfully. {validation_message}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error saving worktree config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/suggestions")
async def get_suggested_directories():
    """Get suggested directories for worktree configuration"""
    try:
        service = WorktreeConfigService()
        suggestions = service.list_suggested_directories()
        
        return {
            "suggestions": suggestions,
            "message": "Suggested directories retrieved successfully"
        }
    except Exception as e:
        logger.error(f"Error getting suggestions: {e}")
        raise HTTPException(status_code=500, detail=str(e))