"""
Result Aggregator Service for Multi-Agent Review System

This service combines individual agent results into a comprehensive markdown report
that matches the format expected by the frontend (similar to the old single-agent system).
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from src.agents.models import MultiAgentResult, AgentResult as OrchAgentResult
from src.models.api_models import AgentType, ReviewResult


class ResultAggregatorService:
    """Aggregates multi-agent results into a unified markdown report"""
    
    def __init__(self):
        self.agent_descriptions = {
            AgentType.ACCEPTANCE_CRITERIA: "Acceptance Criteria Compliance",
            AgentType.BUG_DETECTION: "Bug Detection & Code Issues",
            AgentType.CODE_QUALITY: "Code Quality & Best Practices",
            AgentType.LOGIC_ANALYSIS: "Logic Analysis & Flow",
            AgentType.SECURITY_ANALYSIS: "Security Analysis",
            AgentType.ARCHITECTURAL: "Architectural Review",
            AgentType.SUMMARY: "Summary & Documentation"
        }
    
    def generate_comprehensive_markdown_report(
        self,
        multi_agent_result: MultiAgentResult,
        context_data: Dict[str, Any],
        execution_time: float
    ) -> str:
        """Generate a comprehensive markdown report from all agent results"""
        
        report_sections = []
        
        # Header
        report_sections.append(self._generate_header(context_data, execution_time))
        
        # Executive Summary
        report_sections.append(self._generate_executive_summary(multi_agent_result))
        
        # Agent Results
        report_sections.append(self._generate_agent_results_section(multi_agent_result))
        
        # Key Findings
        report_sections.append(self._generate_key_findings_section(multi_agent_result))
        
        # Recommendations
        report_sections.append(self._generate_recommendations_section(multi_agent_result))
        
        # Performance Metrics
        report_sections.append(self._generate_performance_section(multi_agent_result, execution_time))
        
        # Footer
        report_sections.append(self._generate_footer())
        
        return "\n\n".join(report_sections)
    
    def _generate_header(self, context_data: Dict[str, Any], execution_time: float) -> str:
        """Generate report header"""
        branch_name = context_data.get("branch_name", "unknown")
        review_mode = context_data.get("review_mode", "full")
        timestamp = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC")
        
        return f"""# Multi-Agent Code Review Report

## 📋 Review Overview

- **Branch**: `{branch_name}`
- **Review Mode**: {review_mode.title()}
- **Generated**: {timestamp}
- **Total Execution Time**: {execution_time:.2f} seconds
- **Review Type**: Multi-Agent Parallel Analysis"""
    
    def _generate_executive_summary(self, multi_agent_result: MultiAgentResult) -> str:
        """Generate executive summary"""
        total_agents = multi_agent_result.total_agents
        successful_agents = multi_agent_result.successful_agents
        failed_agents = multi_agent_result.failed_agents
        
        # Count total findings across all agents
        total_findings = 0
        high_priority_findings = 0
        
        for agent_result in multi_agent_result.agent_results.values():
            if agent_result.success and agent_result.result_data:
                findings = agent_result.result_data.get('findings', [])
                total_findings += len(findings)
                # Count high priority findings
                for finding in findings:
                    if isinstance(finding, dict) and finding.get('priority') == 'high':
                        high_priority_findings += 1
        
        success_rate = (successful_agents / total_agents * 100) if total_agents > 0 else 0
        
        status_icon = "✅" if success_rate >= 90 else "⚠️" if success_rate >= 70 else "❌"
        
        return f"""## {status_icon} Executive Summary

**Review Status**: {multi_agent_result.overall_status.value.title()}  
**Success Rate**: {success_rate:.1f}% ({successful_agents}/{total_agents} agents completed successfully)  
**Total Findings**: {total_findings} ({high_priority_findings} high priority)  
**Overall Assessment**: {"PASSED" if success_rate >= 90 else "NEEDS ATTENTION" if success_rate >= 70 else "FAILED"}"""
    
    def _generate_agent_results_section(self, multi_agent_result: MultiAgentResult) -> str:
        """Generate detailed agent results section"""
        sections = ["## 🤖 Agent Analysis Results"]
        
        for agent_type_str, agent_result in multi_agent_result.agent_results.items():
            try:
                agent_type = AgentType(agent_type_str)
                agent_name = self.agent_descriptions.get(agent_type, agent_type_str.title())
                
                # Status icon
                status_icon = "✅" if agent_result.success else "❌"
                
                # Agent header
                sections.append(f"### {status_icon} {agent_name}")
                
                # Execution info
                exec_time = agent_result.execution_time_seconds
                confidence = agent_result.confidence_score
                sections.append(f"**Execution Time**: {exec_time:.2f}s | **Confidence**: {confidence:.1f}%")
                
                if agent_result.success and agent_result.result_data:
                    # Add findings
                    findings = agent_result.result_data.get('findings', [])
                    if findings:
                        sections.append("**Findings:**")
                        for i, finding in enumerate(findings[:5], 1):  # Limit to 5 findings per agent
                            if isinstance(finding, dict):
                                priority = finding.get('priority', 'medium')
                                description = finding.get('description', finding.get('message', str(finding)))
                                priority_icon = "🔴" if priority == 'high' else "🟡" if priority == 'medium' else "🟢"
                                sections.append(f"{i}. {priority_icon} {description}")
                            else:
                                sections.append(f"{i}. {finding}")
                    
                    # Add recommendations
                    recommendations = agent_result.result_data.get('recommendations', [])
                    if recommendations:
                        sections.append("**Recommendations:**")
                        for i, rec in enumerate(recommendations[:3], 1):  # Limit to 3 recommendations
                            if isinstance(rec, dict):
                                rec_text = rec.get('description', rec.get('message', str(rec)))
                            else:
                                rec_text = str(rec)
                            sections.append(f"{i}. 💡 {rec_text}")
                else:
                    sections.append("❌ **Analysis failed or returned no results**")
                
                # Add separator
                sections.append("---")
                
            except Exception as e:
                sections.append(f"⚠️ Error processing agent {agent_type_str}: {str(e)}")
        
        return "\n\n".join(sections)
    
    def _generate_key_findings_section(self, multi_agent_result: MultiAgentResult) -> str:
        """Generate key findings summary"""
        all_findings = []
        
        # Collect all findings from all agents
        for agent_result in multi_agent_result.agent_results.values():
            if agent_result.success and agent_result.result_data:
                findings = agent_result.result_data.get('findings', [])
                for finding in findings:
                    if isinstance(finding, dict):
                        finding['agent'] = agent_result.result_data.get('agent_type', 'unknown')
                        all_findings.append(finding)
        
        if not all_findings:
            return "## 🔍 Key Findings\n\n✅ **No significant issues found!**"
        
        # Sort by priority (high -> medium -> low)
        priority_order = {'high': 0, 'medium': 1, 'low': 2}
        all_findings.sort(key=lambda x: priority_order.get(x.get('priority', 'medium'), 1))
        
        sections = ["## 🔍 Key Findings"]
        
        # Group by priority
        by_priority = {'high': [], 'medium': [], 'low': []}
        for finding in all_findings:
            priority = finding.get('priority', 'medium')
            by_priority[priority].append(finding)
        
        for priority, icon in [('high', '🔴'), ('medium', '🟡'), ('low', '🟢')]:
            findings = by_priority[priority]
            if findings:
                sections.append(f"### {icon} {priority.title()} Priority ({len(findings)} items)")
                for i, finding in enumerate(findings[:10], 1):  # Limit to 10 per priority
                    description = finding.get('description', finding.get('message', 'No description'))
                    agent = finding.get('agent', 'unknown')
                    file_info = f" in `{finding['file']}`" if finding.get('file') else ""
                    sections.append(f"{i}. **{description}**{file_info} _(from {agent})_")
        
        return "\n\n".join(sections)
    
    def _generate_recommendations_section(self, multi_agent_result: MultiAgentResult) -> str:
        """Generate recommendations summary"""
        all_recommendations = []
        
        # Collect all recommendations from all agents
        for agent_result in multi_agent_result.agent_results.values():
            if agent_result.success and agent_result.result_data:
                recommendations = agent_result.result_data.get('recommendations', [])
                for rec in recommendations:
                    if isinstance(rec, dict):
                        rec['agent'] = agent_result.result_data.get('agent_type', 'unknown')
                        all_recommendations.append(rec)
                    elif isinstance(rec, str):
                        all_recommendations.append({
                            'description': rec,
                            'agent': agent_result.result_data.get('agent_type', 'unknown')
                        })
        
        if not all_recommendations:
            return "## 💡 Recommendations\n\n✅ **No specific recommendations at this time.**"
        
        sections = ["## 💡 Recommendations"]
        
        for i, rec in enumerate(all_recommendations[:15], 1):  # Limit to 15 recommendations
            description = rec.get('description', rec.get('message', str(rec)))
            agent = rec.get('agent', 'unknown')
            sections.append(f"{i}. {description} _(from {agent})_")
        
        return "\n\n".join(sections)
    
    def _generate_performance_section(self, multi_agent_result: MultiAgentResult, total_execution_time: float) -> str:
        """Generate performance metrics section"""
        sections = ["## ⚡ Performance Metrics"]
        
        # Overall metrics
        sections.append(f"- **Total Execution Time**: {total_execution_time:.2f} seconds")
        sections.append(f"- **Parallel Efficiency**: {(multi_agent_result.total_execution_time_seconds or 0):.2f}s (theoretical) vs {total_execution_time:.2f}s (actual)")
        sections.append(f"- **Agents Executed**: {multi_agent_result.total_agents}")
        sections.append(f"- **Success Rate**: {(multi_agent_result.successful_agents / multi_agent_result.total_agents * 100):.1f}%")
        
        # Per-agent performance
        sections.append("\n### Agent Performance Breakdown")
        sections.append("| Agent | Status | Execution Time | Confidence |")
        sections.append("|-------|--------|----------------|------------|")
        
        for agent_type_str, agent_result in multi_agent_result.agent_results.items():
            try:
                agent_type = AgentType(agent_type_str)
                agent_name = self.agent_descriptions.get(agent_type, agent_type_str.title())
                status = "✅ Success" if agent_result.success else "❌ Failed"
                exec_time = f"{agent_result.execution_time_seconds:.2f}s"
                confidence = f"{agent_result.confidence_score:.1f}%"
                sections.append(f"| {agent_name} | {status} | {exec_time} | {confidence} |")
            except Exception as e:
                sections.append(f"| {agent_type_str} | ⚠️ Error | - | - |")
        
        return "\n".join(sections)
    
    def _generate_footer(self) -> str:
        """Generate report footer"""
        return f"""---

## 📊 Report Generation Info

- **Generated by**: Multi-Agent Code Review System
- **Architecture**: Parallel Agent Execution
- **Timestamp**: {datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC")}
- **Version**: 2.0.0-multi-agent

> This report was automatically generated by the Multi-Agent Code Review System. Each section represents analysis from specialized agents working in parallel to provide comprehensive code review coverage."""
    
    def extract_priority_findings(self, multi_agent_result: MultiAgentResult) -> List[Dict[str, Any]]:
        """Extract priority findings for the API response"""
        priority_findings = []
        
        for agent_type_str, agent_result in multi_agent_result.agent_results.items():
            if agent_result.success and agent_result.result_data:
                findings = agent_result.result_data.get('findings', [])
                for finding in findings:
                    if isinstance(finding, dict):
                        priority_finding = {
                            'priority': finding.get('priority', 'medium'),
                            'type': finding.get('type', 'general'),
                            'description': finding.get('description', finding.get('message', 'No description')),
                            'agent_type': agent_type_str,
                            'file': finding.get('file'),
                            'line': finding.get('line'),
                            'suggestion': finding.get('suggestion', finding.get('recommendation'))
                        }
                        priority_findings.append(priority_finding)
        
        # Sort by priority
        priority_order = {'high': 0, 'medium': 1, 'low': 2}
        priority_findings.sort(key=lambda x: priority_order.get(x.get('priority', 'medium'), 1))
        
        return priority_findings[:20]  # Limit to top 20 findings