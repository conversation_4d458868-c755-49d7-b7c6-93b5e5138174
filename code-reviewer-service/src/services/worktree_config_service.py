import json
from pathlib import Path
from typing import Dict, Optional, Tuple, Any
from dataclasses import dataclass
import logging
import subprocess

logger = logging.getLogger(__name__)

# Configuration paths
DEVTOOLS_ROOT = Path(__file__).parent.parent.parent.parent  # /Users/<USER>/.devtools
CONFIG_DIR = DEVTOOLS_ROOT / "config"
WORKTREE_CONFIG_FILE = CONFIG_DIR / "worktree-config.json"

def ensure_config_dir():
    """Ensure config directory exists"""
    CONFIG_DIR.mkdir(exist_ok=True)

def get_worktree_config(user_id: str = "default") -> Dict[str, Any]:
    """Get worktree configuration for user from frontend"""
    ensure_config_dir()
    
    try:
        if WORKTREE_CONFIG_FILE.exists():
            with open(WORKTREE_CONFIG_FILE, 'r') as f:
                data = json.load(f)
                user_config = data.get(user_id, {})
                return {
                    "base_path": user_config.get("base_path", ""),
                    "is_valid": user_config.get("is_valid", False),
                    "last_validated": user_config.get("last_validated", ""),
                    "user_id": user_id
                }
    except Exception as e:
        logger.warning(f"Failed to load worktree config: {e}")
    
    return {
        "base_path": "",
        "is_valid": False,
        "last_validated": "",
        "user_id": user_id
    }

def save_worktree_config(base_path: str, is_valid: bool, last_validated: str, user_id: str = "default") -> bool:
    """Save worktree configuration from frontend"""
    ensure_config_dir()
    
    try:
        data = {}
        if WORKTREE_CONFIG_FILE.exists():
            with open(WORKTREE_CONFIG_FILE, 'r') as f:
                data = json.load(f)
        
        data[user_id] = {
            "base_path": base_path,
            "is_valid": is_valid,
            "last_validated": last_validated
        }
        
        with open(WORKTREE_CONFIG_FILE, 'w') as f:
            json.dump(data, f, indent=2)
        
        logger.info(f"✅ Saved worktree config from frontend: {base_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to save worktree config: {e}")
        return False

@dataclass
class WorktreeConfig:
    base_path: str
    is_valid: bool
    last_validated: str
    user_id: str = "default"

class WorktreeConfigService:
    """Service for managing user-configurable worktree paths"""
    
    def __init__(self):
        pass  # No longer need to manage config directory
    
    def get_config(self, user_id: str = "default") -> WorktreeConfig:
        """Get worktree configuration for user"""
        try:
            config_data = get_worktree_config(user_id)
            return WorktreeConfig(
                base_path=config_data["base_path"],
                is_valid=config_data["is_valid"],
                last_validated=config_data["last_validated"],
                user_id=config_data["user_id"]
            )
        except Exception as e:
            logger.warning(f"Failed to load config: {e}")
            # Return empty config - no fallback
            return WorktreeConfig(
                base_path="",
                is_valid=False,
                last_validated="",
                user_id=user_id
            )
    
    def save_config(self, config: WorktreeConfig) -> bool:
        """Save worktree configuration"""
        return save_worktree_config(
            base_path=config.base_path,
            is_valid=config.is_valid,
            last_validated=config.last_validated,
            user_id=config.user_id
        )
    
    def validate_path(self, path: str) -> Tuple[bool, str]:
        """Validate if path is suitable as a master repository for worktrees"""
        try:
            path_obj = Path(path).expanduser().resolve()
            
            # Check if path exists
            if not path_obj.exists():
                return False, f"Path does not exist: {path}"
            
            # Check if it's a directory
            if not path_obj.is_dir():
                return False, f"Path is not a directory: {path}"
            
            # Check if it's a Git repository
            git_dir = path_obj / ".git"
            if not git_dir.exists():
                return False, f"Path is not a Git repository (no .git directory found): {path}"
            
            # Check if git is available
            try:
                subprocess.run(['git', '--version'], 
                             capture_output=True, check=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                return False, "Git is not available in system PATH"
            
            # Check if we can access the repository
            try:
                result = subprocess.run(['git', '-C', str(path_obj), 'status', '--porcelain'], 
                                     capture_output=True, check=True, text=True)
                # If git status works, it's a valid repository
            except subprocess.CalledProcessError as e:
                return False, f"Git repository is not accessible: {e.stderr.strip() if e.stderr else 'Unknown git error'}"
            
            # Check write permissions to parent directory (for creating worktrees outside)
            parent_dir = path_obj.parent
            test_file = parent_dir / ".worktree-test"
            try:
                test_file.touch()
                test_file.unlink()
            except PermissionError:
                return False, f"No write permission in parent directory {parent_dir} (needed for worktree creation)"
            
            return True, f"✅ Valid Git repository! Worktrees will be created in {parent_dir}/worktrees/"
            
        except Exception as e:
            return False, f"Path validation error: {str(e)}"
    
    def list_suggested_directories(self) -> list:
        """List commonly used directories for worktrees"""
        suggestions = [
            {"path": str(Path.home() / "dev"), "name": "~/dev"},
            {"path": str(Path.home() / "projects"), "name": "~/projects"},
            {"path": str(Path.home() / "workspace"), "name": "~/workspace"},
            {"path": "/tmp/worktrees", "name": "/tmp/worktrees"},
        ]
        
        # Filter to only existing directories
        return [s for s in suggestions if Path(s["path"]).exists()]