"""
Migration Service for Multi-Agent Architecture Transition
Handles the migration from sequential to parallel execution with rollback capabilities.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple, Union
from enum import Enum
from dataclasses import dataclass, field
import json
from pathlib import Path
import traceback

from src.config.feature_flags import (
    FeatureFlagManager, ExecutionMode, FeatureFlagStatus,
    get_feature_flag_manager, is_feature_enabled
)
from src.orchestrator.parallel_orchestrator import ParallelMultiAgentOrchestrator
from src.models.api_models import ReviewMode, ReviewRequest, ReviewResponse
from src.agents.models import MultiAgentResult


logger = logging.getLogger(__name__)


class MigrationPhase(str, Enum):
    """Phases of the migration process."""
    PLANNING = "planning"
    TESTING = "testing"
    CANARY = "canary"
    GRADUAL_ROLLOUT = "gradual_rollout"
    FULL_DEPLOYMENT = "full_deployment"
    ROLLBACK = "rollback"
    COMPLETE = "complete"


class MigrationStatus(str, Enum):
    """Status of migration operations."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    SUCCESSFUL = "successful"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"


@dataclass
class MigrationMetrics:
    """Metrics collected during migration."""
    sequential_executions: int = 0
    parallel_executions: int = 0
    sequential_avg_time: float = 0.0
    parallel_avg_time: float = 0.0
    sequential_errors: int = 0
    parallel_errors: int = 0
    sequential_success_rate: float = 100.0
    parallel_success_rate: float = 100.0
    performance_improvement: float = 0.0
    
    # Detailed timing metrics
    sequential_times: List[float] = field(default_factory=list)
    parallel_times: List[float] = field(default_factory=list)
    
    def update_sequential(self, execution_time: float, success: bool):
        """Update sequential execution metrics."""
        self.sequential_executions += 1
        self.sequential_times.append(execution_time)
        
        if not success:
            self.sequential_errors += 1
        
        self.sequential_avg_time = sum(self.sequential_times) / len(self.sequential_times)
        self.sequential_success_rate = ((self.sequential_executions - self.sequential_errors) / 
                                       max(self.sequential_executions, 1)) * 100
        self._calculate_performance_improvement()
    
    def update_parallel(self, execution_time: float, success: bool):
        """Update parallel execution metrics."""
        self.parallel_executions += 1
        self.parallel_times.append(execution_time)
        
        if not success:
            self.parallel_errors += 1
        
        self.parallel_avg_time = sum(self.parallel_times) / len(self.parallel_times)
        self.parallel_success_rate = ((self.parallel_executions - self.parallel_errors) / 
                                     max(self.parallel_executions, 1)) * 100
        self._calculate_performance_improvement()
    
    def _calculate_performance_improvement(self):
        """Calculate performance improvement percentage."""
        if self.sequential_avg_time > 0 and self.parallel_avg_time > 0:
            self.performance_improvement = ((self.sequential_avg_time - self.parallel_avg_time) / 
                                          self.sequential_avg_time) * 100
    
    def get_summary(self) -> Dict[str, Any]:
        """Get metrics summary."""
        return {
            "sequential_executions": self.sequential_executions,
            "parallel_executions": self.parallel_executions,
            "sequential_avg_time": round(self.sequential_avg_time, 2),
            "parallel_avg_time": round(self.parallel_avg_time, 2),
            "sequential_success_rate": round(self.sequential_success_rate, 2),
            "parallel_success_rate": round(self.parallel_success_rate, 2),
            "performance_improvement": round(self.performance_improvement, 2),
            "speed_improvement": round(self.sequential_avg_time / max(self.parallel_avg_time, 0.1), 2)
        }


@dataclass
class MigrationConfig:
    """Configuration for migration process."""
    # Phase timings (in minutes)
    testing_duration: int = 60
    canary_duration: int = 120
    gradual_rollout_step_duration: int = 30
    
    # Safety thresholds
    max_error_rate_threshold: float = 5.0  # %
    min_performance_improvement: float = 200.0  # % (2x speedup)
    min_success_rate: float = 95.0  # %
    
    # Rollout configuration
    canary_user_percentage: float = 5.0  # %
    gradual_rollout_steps: List[float] = field(default_factory=lambda: [10, 25, 50, 75, 100])
    
    # Rollback settings
    auto_rollback_enabled: bool = True
    rollback_on_error_spike: bool = True
    error_spike_threshold: float = 10.0  # %


class MigrationService:
    """Service for managing the migration from sequential to parallel execution."""
    
    def __init__(self, 
                 feature_flag_manager: Optional[FeatureFlagManager] = None,
                 config: Optional[MigrationConfig] = None):
        self.feature_flag_manager = feature_flag_manager or get_feature_flag_manager()
        self.config = config or MigrationConfig()
        self.metrics = MigrationMetrics()
        
        self.current_phase = MigrationPhase.PLANNING
        self.migration_start_time: Optional[datetime] = None
        self.phase_start_time: Optional[datetime] = None
        
        # Migration state
        self.migration_history: List[Dict[str, Any]] = []
        self.rollback_snapshots: List[Dict[str, Any]] = []
        
        self._migration_task: Optional[asyncio.Task] = None
        self._monitoring_task: Optional[asyncio.Task] = None
    
    async def start_migration(self) -> Dict[str, Any]:
        """Start the complete migration process."""
        try:
            logger.info("Starting multi-agent architecture migration")
            
            self.migration_start_time = datetime.now()
            self.current_phase = MigrationPhase.TESTING
            self.phase_start_time = datetime.now()
            
            # Create rollback snapshot
            await self._create_rollback_snapshot("pre_migration")
            
            # Start migration monitoring
            self._monitoring_task = asyncio.create_task(self._monitor_migration())
            
            # Execute migration phases
            self._migration_task = asyncio.create_task(self._execute_migration_phases())
            
            return {
                "status": "started",
                "phase": self.current_phase.value,
                "start_time": self.migration_start_time.isoformat(),
                "estimated_completion": self._estimate_completion_time().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to start migration: {e}")
            await self._handle_migration_failure("start_migration", str(e))
            raise
    
    async def _execute_migration_phases(self):
        """Execute all migration phases in sequence."""
        try:
            phases = [
                (MigrationPhase.TESTING, self._execute_testing_phase),
                (MigrationPhase.CANARY, self._execute_canary_phase),
                (MigrationPhase.GRADUAL_ROLLOUT, self._execute_gradual_rollout_phase),
                (MigrationPhase.FULL_DEPLOYMENT, self._execute_full_deployment_phase)
            ]
            
            for phase, executor in phases:
                logger.info(f"Starting migration phase: {phase.value}")
                self.current_phase = phase
                self.phase_start_time = datetime.now()
                
                # Execute phase
                success = await executor()
                
                if not success:
                    logger.error(f"Migration phase {phase.value} failed")
                    await self._initiate_rollback(f"Phase {phase.value} failed")
                    return
                
                # Record phase completion
                self._record_phase_completion(phase)
                
                # Safety check before next phase
                if not await self._safety_check():
                    logger.warning("Safety check failed, initiating rollback")
                    await self._initiate_rollback("Safety check failed")
                    return
            
            # Migration completed successfully
            self.current_phase = MigrationPhase.COMPLETE
            logger.info("Migration completed successfully")
            await self._finalize_migration()
            
        except Exception as e:
            logger.error(f"Migration execution failed: {e}")
            await self._initiate_rollback(f"Execution error: {str(e)}")
    
    async def _execute_testing_phase(self) -> bool:
        """Execute testing phase with A/B testing."""
        try:
            logger.info("Starting testing phase with A/B testing")
            
            # Enable A/B testing flag
            self.feature_flag_manager.enable_flag("ab_test_parallel_vs_sequential")
            
            # Update rollout percentage for 50/50 split
            self.feature_flag_manager.update_rollout_percentage("ab_test_parallel_vs_sequential", 50.0)
            
            # Run testing for configured duration
            await asyncio.sleep(self.config.testing_duration * 60)
            
            # Analyze results
            analysis = await self._analyze_testing_results()
            
            if analysis["success"]:
                logger.info(f"Testing phase successful: {analysis['summary']}")
                return True
            else:
                logger.error(f"Testing phase failed: {analysis['summary']}")
                return False
                
        except Exception as e:
            logger.error(f"Testing phase error: {e}")
            return False
    
    async def _execute_canary_phase(self) -> bool:
        """Execute canary deployment phase."""
        try:
            logger.info("Starting canary deployment phase")
            
            # Disable A/B test and enable canary
            self.feature_flag_manager.disable_flag("ab_test_parallel_vs_sequential")
            
            # Enable main flag with canary rollout
            flag = self.feature_flag_manager.flags["multi_agent_execution"]
            flag.status = FeatureFlagStatus.ENABLED
            if flag.rollout_config is None:
                flag.rollout_config = {}
            flag.rollout_config.update({
                "strategy": "canary",
                "percentage": self.config.canary_user_percentage,
                "canary_user_groups": {"early_adopters", "beta_testers"}
            })
            self.feature_flag_manager.save_flags()
            
            # Monitor canary deployment
            await asyncio.sleep(self.config.canary_duration * 60)
            
            # Analyze canary results
            analysis = await self._analyze_canary_results()
            
            if analysis["success"]:
                logger.info(f"Canary phase successful: {analysis['summary']}")
                return True
            else:
                logger.error(f"Canary phase failed: {analysis['summary']}")
                return False
                
        except Exception as e:
            logger.error(f"Canary phase error: {e}")
            return False
    
    async def _execute_gradual_rollout_phase(self) -> bool:
        """Execute gradual rollout phase."""
        try:
            logger.info("Starting gradual rollout phase")
            
            # Switch to gradual rollout strategy
            flag = self.feature_flag_manager.flags["multi_agent_execution"]
            if flag.rollout_config is None:
                flag.rollout_config = {}
            flag.rollout_config.update({
                "strategy": "gradual",
                "percentage": 0.0,
                "target_percentage": 100.0,
                "increment_step": 10.0
            })
            
            # Execute gradual rollout steps
            for step_percentage in self.config.gradual_rollout_steps[:-1]:  # Exclude 100%
                logger.info(f"Rolling out to {step_percentage}% of users")
                
                self.feature_flag_manager.update_rollout_percentage("multi_agent_execution", step_percentage)
                
                # Wait for step duration
                await asyncio.sleep(self.config.gradual_rollout_step_duration * 60)
                
                # Check metrics at each step
                if not await self._check_rollout_step_health():
                    logger.error(f"Health check failed at {step_percentage}% rollout")
                    return False
            
            logger.info("Gradual rollout phase completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Gradual rollout phase error: {e}")
            return False
    
    async def _execute_full_deployment_phase(self) -> bool:
        """Execute full deployment phase."""
        try:
            logger.info("Starting full deployment phase")
            
            # Roll out to 100% of users
            self.feature_flag_manager.update_rollout_percentage("multi_agent_execution", 100.0)
            
            # Enable all agent-specific flags
            agent_types = [
                "acceptance_criteria", "bug_detection", "security_analysis",
                "code_quality", "performance_analysis", "architectural_analysis", "summary_agent"
            ]
            
            for agent_type in agent_types:
                self.feature_flag_manager.enable_flag(f"parallel_{agent_type}")
            
            # Final monitoring period
            await asyncio.sleep(30 * 60)  # 30 minutes
            
            # Final health check
            if await self._final_health_check():
                logger.info("Full deployment phase successful")
                return True
            else:
                logger.error("Full deployment phase failed final health check")
                return False
                
        except Exception as e:
            logger.error(f"Full deployment phase error: {e}")
            return False
    
    async def _monitor_migration(self):
        """Monitor migration progress and metrics continuously."""
        while self.current_phase != MigrationPhase.COMPLETE and self.current_phase != MigrationPhase.ROLLBACK:
            try:
                # Collect current metrics
                await self._collect_metrics()
                
                # Check for error spikes
                if self.config.rollback_on_error_spike:
                    if await self._detect_error_spike():
                        logger.warning("Error spike detected, initiating rollback")
                        await self._initiate_rollback("Error spike detected")
                        break
                
                # Check migration timeout
                if self._is_migration_timeout():
                    logger.warning("Migration timeout reached, initiating rollback")
                    await self._initiate_rollback("Migration timeout")
                    break
                
                await asyncio.sleep(60)  # Check every minute
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Migration monitoring error: {e}")
    
    async def _collect_metrics(self):
        """Collect current migration metrics."""
        # This would integrate with the actual orchestrator and monitoring systems
        # For now, we'll simulate metric collection
        pass
    
    async def _detect_error_spike(self) -> bool:
        """Detect if there's an error spike indicating problems."""
        if self.metrics.parallel_executions < 10:
            return False  # Not enough data
        
        current_error_rate = ((self.metrics.parallel_errors / self.metrics.parallel_executions) * 100)
        return current_error_rate > self.config.error_spike_threshold
    
    def _is_migration_timeout(self) -> bool:
        """Check if migration has exceeded maximum allowed time."""
        if not self.migration_start_time:
            return False
        
        max_duration = timedelta(hours=8)  # 8 hours max
        return (datetime.now() - self.migration_start_time) > max_duration
    
    async def _safety_check(self) -> bool:
        """Perform safety check before proceeding to next phase."""
        try:
            # Check error rates
            if self.metrics.parallel_success_rate < self.config.min_success_rate:
                logger.warning(f"Parallel success rate too low: {self.metrics.parallel_success_rate}%")
                return False
            
            # Check performance improvement
            if (self.metrics.parallel_executions > 10 and 
                self.metrics.performance_improvement < self.config.min_performance_improvement):
                logger.warning(f"Performance improvement insufficient: {self.metrics.performance_improvement}%")
                return False
            
            # Check for critical errors
            recent_errors = await self._get_recent_critical_errors()
            if len(recent_errors) > 5:
                logger.warning(f"Too many recent critical errors: {len(recent_errors)}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Safety check error: {e}")
            return False
    
    async def _get_recent_critical_errors(self) -> List[Dict[str, Any]]:
        """Get recent critical errors from monitoring systems."""
        # Placeholder for integration with logging/monitoring
        return []
    
    async def _analyze_testing_results(self) -> Dict[str, Any]:
        """Analyze A/B testing results."""
        try:
            if self.metrics.parallel_executions < 10 or self.metrics.sequential_executions < 10:
                return {
                    "success": False,
                    "summary": "Insufficient test data collected",
                    "metrics": self.metrics.get_summary()
                }
            
            # Check success rates
            if self.metrics.parallel_success_rate < self.config.min_success_rate:
                return {
                    "success": False,
                    "summary": f"Parallel success rate too low: {self.metrics.parallel_success_rate}%",
                    "metrics": self.metrics.get_summary()
                }
            
            # Check performance improvement
            if self.metrics.performance_improvement < self.config.min_performance_improvement:
                return {
                    "success": False,
                    "summary": f"Performance improvement insufficient: {self.metrics.performance_improvement}%",
                    "metrics": self.metrics.get_summary()
                }
            
            return {
                "success": True,
                "summary": f"Testing successful: {self.metrics.performance_improvement:.1f}% improvement",
                "metrics": self.metrics.get_summary()
            }
            
        except Exception as e:
            return {
                "success": False,
                "summary": f"Analysis error: {str(e)}",
                "metrics": {}
            }
    
    async def _analyze_canary_results(self) -> Dict[str, Any]:
        """Analyze canary deployment results."""
        # Similar to testing analysis but focused on canary metrics
        return await self._analyze_testing_results()
    
    async def _check_rollout_step_health(self) -> bool:
        """Check health at a rollout step."""
        return await self._safety_check()
    
    async def _final_health_check(self) -> bool:
        """Perform final comprehensive health check."""
        try:
            # Comprehensive checks
            checks = [
                await self._safety_check(),
                await self._check_system_stability(),
                await self._check_performance_metrics(),
                await self._check_error_logs()
            ]
            
            return all(checks)
            
        except Exception as e:
            logger.error(f"Final health check error: {e}")
            return False
    
    async def _check_system_stability(self) -> bool:
        """Check overall system stability."""
        # Placeholder for system stability checks
        return True
    
    async def _check_performance_metrics(self) -> bool:
        """Check performance metrics are acceptable."""
        return self.metrics.performance_improvement >= self.config.min_performance_improvement
    
    async def _check_error_logs(self) -> bool:
        """Check error logs for critical issues."""
        recent_errors = await self._get_recent_critical_errors()
        return len(recent_errors) == 0
    
    async def _initiate_rollback(self, reason: str):
        """Initiate rollback process."""
        try:
            logger.warning(f"Initiating rollback: {reason}")
            
            self.current_phase = MigrationPhase.ROLLBACK
            
            # Cancel migration task
            if self._migration_task and not self._migration_task.done():
                self._migration_task.cancel()
            
            # Disable all parallel execution flags
            await self._disable_parallel_execution()
            
            # Restore from rollback snapshot
            await self._restore_from_snapshot("pre_migration")
            
            # Record rollback
            self._record_rollback(reason)
            
            logger.info("Rollback completed successfully")
            
        except Exception as e:
            logger.error(f"Rollback failed: {e}")
            # Emergency fallback
            await self._emergency_rollback()
    
    async def _disable_parallel_execution(self):
        """Disable all parallel execution features."""
        # Disable main execution flag
        self.feature_flag_manager.rollback_flag("multi_agent_execution", "Migration rollback")
        
        # Disable A/B testing
        self.feature_flag_manager.disable_flag("ab_test_parallel_vs_sequential")
        
        # Disable all agent-specific flags
        agent_types = [
            "acceptance_criteria", "bug_detection", "security_analysis",
            "code_quality", "performance_analysis", "architectural_analysis", "summary_agent"
        ]
        
        for agent_type in agent_types:
            self.feature_flag_manager.disable_flag(f"parallel_{agent_type}")
    
    async def _emergency_rollback(self):
        """Emergency rollback to sequential execution."""
        try:
            # Force disable all parallel features
            for flag_name in self.feature_flag_manager.flags.keys():
                if "parallel" in flag_name or "multi_agent" in flag_name:
                    flag = self.feature_flag_manager.flags[flag_name]
                    flag.status = FeatureFlagStatus.DISABLED
                    flag.execution_mode = ExecutionMode.SEQUENTIAL
            
            self.feature_flag_manager.save_flags()
            logger.info("Emergency rollback completed")
            
        except Exception as e:
            logger.critical(f"Emergency rollback failed: {e}")
    
    async def _create_rollback_snapshot(self, snapshot_name: str):
        """Create a rollback snapshot of current state."""
        try:
            snapshot = {
                "name": snapshot_name,
                "timestamp": datetime.now().isoformat(),
                "feature_flags": self.feature_flag_manager.get_all_flags_status(),
                "configuration": {
                    # Add other configuration that needs to be backed up
                }
            }
            
            self.rollback_snapshots.append(snapshot)
            
            # Save to disk
            snapshots_file = Path("migration_snapshots.json")
            with open(snapshots_file, 'w') as f:
                json.dump(self.rollback_snapshots, f, indent=2)
            
            logger.info(f"Rollback snapshot '{snapshot_name}' created")
            
        except Exception as e:
            logger.error(f"Failed to create rollback snapshot: {e}")
    
    async def _restore_from_snapshot(self, snapshot_name: str):
        """Restore system state from a rollback snapshot."""
        try:
            snapshot = None
            for snap in self.rollback_snapshots:
                if snap["name"] == snapshot_name:
                    snapshot = snap
                    break
            
            if not snapshot:
                logger.error(f"Rollback snapshot '{snapshot_name}' not found")
                return
            
            # Restore feature flags
            flags_status = snapshot["feature_flags"]
            for flag_name, flag_data in flags_status.items():
                if flag_name in self.feature_flag_manager.flags:
                    flag = self.feature_flag_manager.flags[flag_name]
                    flag.status = FeatureFlagStatus(flag_data["status"])
                    flag.updated_at = datetime.now()
            
            self.feature_flag_manager.save_flags()
            logger.info(f"System restored from snapshot '{snapshot_name}'")
            
        except Exception as e:
            logger.error(f"Failed to restore from snapshot: {e}")
    
    def _record_phase_completion(self, phase: MigrationPhase):
        """Record completion of a migration phase."""
        self.migration_history.append({
            "phase": phase.value,
            "status": "completed",
            "start_time": self.phase_start_time.isoformat() if self.phase_start_time else None,
            "end_time": datetime.now().isoformat(),
            "metrics": self.metrics.get_summary()
        })
    
    def _record_rollback(self, reason: str):
        """Record rollback event."""
        self.migration_history.append({
            "phase": "rollback",
            "status": "completed",
            "reason": reason,
            "timestamp": datetime.now().isoformat(),
            "metrics": self.metrics.get_summary()
        })
    
    def _estimate_completion_time(self) -> datetime:
        """Estimate migration completion time."""
        total_minutes = (
            self.config.testing_duration +
            self.config.canary_duration +
            len(self.config.gradual_rollout_steps) * self.config.gradual_rollout_step_duration +
            30  # Full deployment buffer
        )
        
        return datetime.now() + timedelta(minutes=total_minutes)
    
    async def _finalize_migration(self):
        """Finalize successful migration."""
        try:
            # Clean up temporary flags
            self.feature_flag_manager.disable_flag("ab_test_parallel_vs_sequential")
            
            # Record final completion
            self.migration_history.append({
                "phase": "complete",
                "status": "successful",
                "completion_time": datetime.now().isoformat(),
                "total_duration_minutes": (datetime.now() - (self.migration_start_time or datetime.now())).total_seconds() / 60,
                "final_metrics": self.metrics.get_summary()
            })
            
            # Save migration report
            await self._save_migration_report()
            
            logger.info("Migration finalization completed")
            
        except Exception as e:
            logger.error(f"Migration finalization error: {e}")
    
    async def _save_migration_report(self):
        """Save comprehensive migration report."""
        try:
            report = {
                "migration_summary": {
                    "start_time": self.migration_start_time.isoformat() if self.migration_start_time else None,
                    "end_time": datetime.now().isoformat(),
                    "final_phase": self.current_phase.value,
                    "status": "successful" if self.current_phase == MigrationPhase.COMPLETE else "failed"
                },
                "performance_metrics": self.metrics.get_summary(),
                "migration_history": self.migration_history,
                "rollback_snapshots": len(self.rollback_snapshots),
                "final_configuration": self.feature_flag_manager.get_all_flags_status()
            }
            
            report_file = Path(f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Migration report saved to {report_file}")
            
        except Exception as e:
            logger.error(f"Failed to save migration report: {e}")
    
    async def _handle_migration_failure(self, operation: str, error: str):
        """Handle migration failure."""
        self.migration_history.append({
            "operation": operation,
            "status": "failed",
            "error": error,
            "timestamp": datetime.now().isoformat(),
            "traceback": traceback.format_exc()
        })
        
        if self.config.auto_rollback_enabled:
            await self._initiate_rollback(f"Auto rollback due to {operation} failure")
    
    def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status."""
        return {
            "current_phase": self.current_phase.value,
            "start_time": self.migration_start_time.isoformat() if self.migration_start_time else None,
            "phase_start_time": self.phase_start_time.isoformat() if self.phase_start_time else None,
            "metrics": self.metrics.get_summary(),
            "history": self.migration_history[-5:],  # Last 5 events
            "estimated_completion": self._estimate_completion_time().isoformat() if self.current_phase != MigrationPhase.COMPLETE else None
        }
    
    def get_rollback_options(self) -> List[Dict[str, Any]]:
        """Get available rollback options."""
        return [
            {
                "name": snapshot["name"],
                "timestamp": snapshot["timestamp"],
                "description": f"Rollback to {snapshot['name']} state"
            }
            for snapshot in self.rollback_snapshots
        ]
    
    async def manual_rollback(self, snapshot_name: str = "pre_migration") -> Dict[str, Any]:
        """Manually trigger rollback to specified snapshot."""
        try:
            await self._initiate_rollback(f"Manual rollback to {snapshot_name}")
            return {
                "status": "success",
                "message": f"Rollback to {snapshot_name} initiated",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Rollback failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def stop_migration(self) -> Dict[str, Any]:
        """Stop current migration process."""
        try:
            if self._migration_task and not self._migration_task.done():
                self._migration_task.cancel()
            
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
            
            # Rollback to safe state
            await self._initiate_rollback("Migration stopped by user")
            
            return {
                "status": "stopped",
                "message": "Migration stopped and rolled back",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to stop migration: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }